{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,cAAc,EAAE,MAAM,iBAAiB,CAAC;AAIjD,MAAM,MAAM,GAAG,cAAc,CAAe,QAAQ,EAAE;IACpD,GAAG,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,CAAC;CACxD,CAAC,CAAC;AAEH,cAAc,eAAe,CAAC;AAC9B,OAAO,EAAE,MAAM,EAAE,CAAC", "sourcesContent": ["import { registerPlugin } from '@capacitor/core';\n\nimport type { DevicePlugin } from './definitions';\n\nconst Device = registerPlugin<DevicePlugin>('Device', {\n  web: () => import('./web').then(m => new m.DeviceWeb()),\n});\n\nexport * from './definitions';\nexport { Device };\n"]}