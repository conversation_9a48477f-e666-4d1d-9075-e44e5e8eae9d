{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "web.js", "sourceRoot": "", "sources": ["file:///C:/QuantAxiom/Products/LifeManager/node_modules/%40capacitor/network/src/web.ts"], "sourcesContent": ["import { WebPlugin } from '@capacitor/core';\n\nimport type {\n  ConnectionStatus,\n  ConnectionType,\n  NetworkPlugin,\n} from './definitions';\n\ndeclare global {\n  interface Navigator {\n    connection: any;\n    mozConnection: any;\n    webkitConnection: any;\n  }\n}\n\nfunction translatedConnection(): ConnectionType {\n  const connection =\n    window.navigator.connection ||\n    window.navigator.mozConnection ||\n    window.navigator.webkitConnection;\n  let result: ConnectionType = 'unknown';\n  const type = connection ? connection.type || connection.effectiveType : null;\n  if (type && typeof type === 'string') {\n    switch (type) {\n      // possible type values\n      case 'bluetooth':\n      case 'cellular':\n        result = 'cellular';\n        break;\n      case 'none':\n        result = 'none';\n        break;\n      case 'ethernet':\n      case 'wifi':\n      case 'wimax':\n        result = 'wifi';\n        break;\n      case 'other':\n      case 'unknown':\n        result = 'unknown';\n        break;\n      // possible effectiveType values\n      case 'slow-2g':\n      case '2g':\n      case '3g':\n        result = 'cellular';\n        break;\n      case '4g':\n        result = 'wifi';\n        break;\n      default:\n        break;\n    }\n  }\n  return result;\n}\n\nexport class NetworkWeb extends WebPlugin implements NetworkPlugin {\n  constructor() {\n    super();\n    if (typeof window !== 'undefined') {\n      window.addEventListener('online', this.handleOnline);\n      window.addEventListener('offline', this.handleOffline);\n    }\n  }\n\n  async getStatus(): Promise<ConnectionStatus> {\n    if (!window.navigator) {\n      throw this.unavailable(\n        'Browser does not support the Network Information API',\n      );\n    }\n\n    const connected = window.navigator.onLine;\n    const connectionType = translatedConnection();\n\n    const status: ConnectionStatus = {\n      connected,\n      connectionType: connected ? connectionType : 'none',\n    };\n\n    return status;\n  }\n\n  private handleOnline = () => {\n    const connectionType = translatedConnection();\n\n    const status: ConnectionStatus = {\n      connected: true,\n      connectionType: connectionType,\n    };\n\n    this.notifyListeners('networkStatusChange', status);\n  };\n\n  private handleOffline = () => {\n    const status: ConnectionStatus = {\n      connected: false,\n      connectionType: 'none',\n    };\n\n    this.notifyListeners('networkStatusChange', status);\n  };\n}\n\nconst Network = new NetworkWeb();\n\nexport { Network };\n"], "names": [], "mappings": ";;;;AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;;AAgB5C,SAAS,oBAAoB;IAC3B,MAAM,UAAU,GACd,MAAM,CAAC,SAAS,CAAC,UAAU,IAC3B,MAAM,CAAC,SAAS,CAAC,aAAa,IAC9B,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;IACpC,IAAI,MAAM,GAAmB,SAAS,CAAC;IACvC,MAAM,IAAI,GAAG,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,IAAI,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC;IAC7E,IAAI,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;QACpC,OAAQ,IAAI,EAAE;YACZ,uBAAuB;YACvB,KAAK,WAAW,CAAC;YACjB,KAAK,UAAU;gBACb,MAAM,GAAG,UAAU,CAAC;gBACpB,MAAM;YACR,KAAK,MAAM;gBACT,MAAM,GAAG,MAAM,CAAC;gBAChB,MAAM;YACR,KAAK,UAAU,CAAC;YAChB,KAAK,MAAM,CAAC;YACZ,KAAK,OAAO;gBACV,MAAM,GAAG,MAAM,CAAC;gBAChB,MAAM;YACR,KAAK,OAAO,CAAC;YACb,KAAK,SAAS;gBACZ,MAAM,GAAG,SAAS,CAAC;gBACnB,MAAM;YACR,gCAAgC;YAChC,KAAK,SAAS,CAAC;YACf,KAAK,IAAI,CAAC;YACV,KAAK,IAAI;gBACP,MAAM,GAAG,UAAU,CAAC;gBACpB,MAAM;YACR,KAAK,IAAI;gBACP,MAAM,GAAG,MAAM,CAAC;gBAChB,MAAM;YACR;gBACE,MAAM;SACT;KACF;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAEK,MAAO,UAAW,8JAAQ,YAAS;IACvC,aAAA;QACE,KAAK,EAAE,CAAC;QAyBF,IAAA,CAAA,YAAY,GAAG,GAAG,EAAE;YAC1B,MAAM,cAAc,GAAG,oBAAoB,EAAE,CAAC;YAE9C,MAAM,MAAM,GAAqB;gBAC/B,SAAS,EAAE,IAAI;gBACf,cAAc,EAAE,cAAc;aAC/B,CAAC;YAEF,IAAI,CAAC,eAAe,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;QACtD,CAAC,CAAC;QAEM,IAAA,CAAA,aAAa,GAAG,GAAG,EAAE;YAC3B,MAAM,MAAM,GAAqB;gBAC/B,SAAS,EAAE,KAAK;gBAChB,cAAc,EAAE,MAAM;aACvB,CAAC;YAEF,IAAI,CAAC,eAAe,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;QACtD,CAAC,CAAC;QA1CA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;YACjC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;YACrD,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;SACxD;IACH,CAAC;IAED,KAAK,CAAC,SAAS,GAAA;QACb,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;YACrB,MAAM,IAAI,CAAC,WAAW,CACpB,sDAAsD,CACvD,CAAC;SACH;QAED,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC;QAC1C,MAAM,cAAc,GAAG,oBAAoB,EAAE,CAAC;QAE9C,MAAM,MAAM,GAAqB;YAC/B,SAAS;YACT,cAAc,EAAE,SAAS,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,MAAM;SACpD,CAAC;QAEF,OAAO,MAAM,CAAC;IAChB,CAAC;CAqBF;AAED,MAAM,OAAO,GAAG,IAAI,UAAU,EAAE,CAAC", "ignoreList": [0], "debugId": null}}]}