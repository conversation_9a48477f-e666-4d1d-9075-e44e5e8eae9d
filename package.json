{"name": "life-manager", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "build:mobile": "cross-env CAPACITOR_BUILD=true next build && next export", "start": "next start", "lint": "next lint", "test": "jest", "cap:sync": "npx cap sync", "cap:build": "npm run build:mobile && npx cap sync", "cap:android": "npm run cap:build && npx cap open android", "cap:run:android": "npm run cap:build && npx cap run android", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@capacitor/android": "^7.4.1", "@capacitor/cli": "^7.4.1", "@capacitor/core": "^7.4.1", "@capacitor/device": "^7.0.1", "@capacitor/haptics": "^7.0.1", "@capacitor/network": "^7.0.1", "@capacitor/share": "^7.0.1", "@capacitor/splash-screen": "^7.0.1", "@capacitor/status-bar": "^7.0.1", "@capacitor/toast": "^7.0.1", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.3", "@types/cheerio": "^0.22.35", "cheerio": "^1.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.23.0", "lucide-react": "^0.525.0", "next": "15.3.5", "next-intl": "^4.3.4", "openai": "^5.8.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "react-hot-toast": "^2.5.2", "recharts": "^3.0.2", "tailwind-merge": "^3.3.1", "zod": "^3.25.75"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "tailwindcss": "^4", "typescript": "^5"}}