{"version": 3, "file": "web.js", "sourceRoot": "", "sources": ["../../src/web.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAS5C,MAAM,OAAO,QAAS,SAAQ,SAAS;IACrC,KAAK,CAAC,QAAQ;QACZ,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;YACxD,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;SACzB;aAAM;YACL,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;SACxB;IACH,CAAC;IACD,KAAK,CAAC,KAAK,CAAC,OAAqB;QAC/B,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;YACxD,MAAM,IAAI,CAAC,WAAW,CAAC,yCAAyC,CAAC,CAAC;SACnE;QAED,MAAM,SAAS,CAAC,KAAK,CAAC;YACpB,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,GAAG,EAAE,OAAO,CAAC,GAAG;SACjB,CAAC,CAAC;QACH,OAAO,EAAE,CAAC;IACZ,CAAC;CACF", "sourcesContent": ["import { WebPlugin } from '@capacitor/core';\n\nimport type {\n  CanShareResult,\n  ShareOptions,\n  SharePlugin,\n  ShareResult,\n} from './definitions';\n\nexport class ShareWeb extends WebPlugin implements SharePlugin {\n  async canShare(): Promise<CanShareResult> {\n    if (typeof navigator === 'undefined' || !navigator.share) {\n      return { value: false };\n    } else {\n      return { value: true };\n    }\n  }\n  async share(options: ShareOptions): Promise<ShareResult> {\n    if (typeof navigator === 'undefined' || !navigator.share) {\n      throw this.unavailable('Share API not available in this browser');\n    }\n\n    await navigator.share({\n      title: options.title,\n      text: options.text,\n      url: options.url,\n    });\n    return {};\n  }\n}\n"]}