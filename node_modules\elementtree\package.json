{"author": "Rackspace US, Inc.", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>"], "name": "elementtree", "description": "XML Serialization and Parsing module based on Python's ElementTree.", "version": "0.1.7", "keywords": ["xml", "sax", "parser", "seralization", "elementtree"], "homepage": "https://github.com/racker/node-elementtree", "repository": {"type": "git", "url": "git://github.com/racker/node-elementtree.git"}, "main": "lib/elementtree.js", "directories": {"lib": "lib"}, "scripts": {"test": "make test"}, "engines": {"node": ">= 0.4.0"}, "dependencies": {"sax": "1.1.4"}, "devDependencies": {"whiskey": "0.8.x"}, "license": "Apache-2.0"}