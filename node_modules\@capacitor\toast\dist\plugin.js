var capacitorToast = (function (exports, core) {
    'use strict';

    const Toast = core.registerPlugin('Toast', {
        web: () => Promise.resolve().then(function () { return web; }).then(m => new m.ToastWeb()),
    });

    class ToastWeb extends core.WebPlugin {
        async show(options) {
            if (typeof document !== 'undefined') {
                let duration = 2000;
                if (options.duration) {
                    duration = options.duration === 'long' ? 3500 : 2000;
                }
                const toast = document.createElement('pwa-toast');
                toast.duration = duration;
                toast.message = options.text;
                document.body.appendChild(toast);
            }
        }
    }

    var web = /*#__PURE__*/Object.freeze({
        __proto__: null,
        ToastWeb: ToastWeb
    });

    exports.Toast = Toast;

    return exports;

})({}, capacitorExports);
//# sourceMappingURL=plugin.js.map
