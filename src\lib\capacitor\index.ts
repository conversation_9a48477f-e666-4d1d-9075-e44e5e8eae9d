import { Capacitor } from '@capacitor/core'
import { StatusBar, Style } from '@capacitor/status-bar'
import { SplashScreen } from '@capacitor/splash-screen'
import { Haptics, ImpactStyle } from '@capacitor/haptics'
import { Toast } from '@capacitor/toast'
import { Share } from '@capacitor/share'
import { Network } from '@capacitor/network'
import { Device } from '@capacitor/device'

export class CapacitorService {
  private static instance: CapacitorService
  private isNative: boolean

  constructor() {
    this.isNative = Capacitor.isNativePlatform()
  }

  static getInstance(): CapacitorService {
    if (!CapacitorService.instance) {
      CapacitorService.instance = new CapacitorService()
    }
    return CapacitorService.instance
  }

  // Platform detection
  isNativePlatform(): boolean {
    return this.isNative
  }

  getPlatform(): string {
    return Capacitor.getPlatform()
  }

  // App lifecycle
  async initializeApp(): Promise<void> {
    if (!this.isNative) return

    try {
      // Configure status bar
      await StatusBar.setStyle({ style: Style.Dark })
      await StatusBar.setBackgroundColor({ color: '#3b82f6' })

      // Hide splash screen after app is ready
      await SplashScreen.hide()

      console.log('Capacitor app initialized successfully')
    } catch (error) {
      console.error('Error initializing Capacitor app:', error)
    }
  }

  // Haptic feedback
  async hapticImpact(style: ImpactStyle = ImpactStyle.Medium): Promise<void> {
    if (!this.isNative) return

    try {
      await Haptics.impact({ style })
    } catch (error) {
      console.error('Error with haptic feedback:', error)
    }
  }

  async hapticNotification(type: 'success' | 'warning' | 'error' = 'success'): Promise<void> {
    if (!this.isNative) return

    try {
      await Haptics.notification({ 
        type: type === 'success' ? 'SUCCESS' : type === 'warning' ? 'WARNING' : 'ERROR' 
      })
    } catch (error) {
      console.error('Error with haptic notification:', error)
    }
  }

  // Toast notifications
  async showToast(message: string, duration: 'short' | 'long' = 'short'): Promise<void> {
    if (!this.isNative) {
      // Fallback for web
      console.log('Toast:', message)
      return
    }

    try {
      await Toast.show({
        text: message,
        duration: duration
      })
    } catch (error) {
      console.error('Error showing toast:', error)
    }
  }

  // Sharing
  async share(options: {
    title?: string
    text?: string
    url?: string
    dialogTitle?: string
  }): Promise<void> {
    if (!this.isNative) {
      // Fallback for web
      if (navigator.share) {
        try {
          await navigator.share(options)
        } catch (error) {
          console.error('Error sharing:', error)
        }
      } else {
        console.log('Share not supported on this platform')
      }
      return
    }

    try {
      await Share.share(options)
    } catch (error) {
      console.error('Error sharing:', error)
    }
  }

  // Network status
  async getNetworkStatus(): Promise<{ connected: boolean; connectionType: string }> {
    try {
      const status = await Network.getStatus()
      return {
        connected: status.connected,
        connectionType: status.connectionType
      }
    } catch (error) {
      console.error('Error getting network status:', error)
      return { connected: true, connectionType: 'unknown' }
    }
  }

  // Device information
  async getDeviceInfo(): Promise<{
    platform: string
    model: string
    osVersion: string
    manufacturer: string
  }> {
    try {
      const info = await Device.getInfo()
      return {
        platform: info.platform,
        model: info.model,
        osVersion: info.osVersion,
        manufacturer: info.manufacturer
      }
    } catch (error) {
      console.error('Error getting device info:', error)
      return {
        platform: 'unknown',
        model: 'unknown',
        osVersion: 'unknown',
        manufacturer: 'unknown'
      }
    }
  }

  // Status bar management
  async setStatusBarStyle(style: 'light' | 'dark'): Promise<void> {
    if (!this.isNative) return

    try {
      await StatusBar.setStyle({ 
        style: style === 'light' ? Style.Light : Style.Dark 
      })
    } catch (error) {
      console.error('Error setting status bar style:', error)
    }
  }

  async hideStatusBar(): Promise<void> {
    if (!this.isNative) return

    try {
      await StatusBar.hide()
    } catch (error) {
      console.error('Error hiding status bar:', error)
    }
  }

  async showStatusBar(): Promise<void> {
    if (!this.isNative) return

    try {
      await StatusBar.show()
    } catch (error) {
      console.error('Error showing status bar:', error)
    }
  }
}

// Export singleton instance
export const capacitorService = CapacitorService.getInstance()

// React hook for using Capacitor features
import { useState, useEffect } from 'react'

export function useCapacitor() {
  const [isNative, setIsNative] = useState(false)
  const [networkStatus, setNetworkStatus] = useState({ connected: true, connectionType: 'unknown' })

  useEffect(() => {
    setIsNative(capacitorService.isNativePlatform())

    // Initialize app if native
    if (capacitorService.isNativePlatform()) {
      capacitorService.initializeApp()
    }

    // Monitor network status
    const updateNetworkStatus = async () => {
      const status = await capacitorService.getNetworkStatus()
      setNetworkStatus(status)
    }

    updateNetworkStatus()

    // Set up network listener if native
    if (capacitorService.isNativePlatform()) {
      const networkListener = Network.addListener('networkStatusChange', (status) => {
        setNetworkStatus({
          connected: status.connected,
          connectionType: status.connectionType
        })
      })

      return () => {
        networkListener.remove()
      }
    }
  }, [])

  return {
    isNative,
    networkStatus,
    hapticImpact: capacitorService.hapticImpact.bind(capacitorService),
    hapticNotification: capacitorService.hapticNotification.bind(capacitorService),
    showToast: capacitorService.showToast.bind(capacitorService),
    share: capacitorService.share.bind(capacitorService),
    setStatusBarStyle: capacitorService.setStatusBarStyle.bind(capacitorService)
  }
}

// Utility functions
export function isMobile(): boolean {
  return capacitorService.isNativePlatform() || 
    (typeof window !== 'undefined' && window.innerWidth <= 768)
}

export function isAndroid(): boolean {
  return capacitorService.getPlatform() === 'android'
}

export function isIOS(): boolean {
  return capacitorService.getPlatform() === 'ios'
}
