{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/providers/ToastProvider.tsx"], "sourcesContent": ["'use client'\n\nimport { Toaster } from 'react-hot-toast'\n\nexport default function ToastProvider() {\n  return (\n    <Toaster\n      position=\"top-right\"\n      toastOptions={{\n        duration: 4000,\n        style: {\n          background: '#fff',\n          color: '#363636',\n          borderRadius: '12px',\n          border: '1px solid #e5e7eb',\n          boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',\n          padding: '16px',\n          fontSize: '14px',\n          fontWeight: '500',\n        },\n        success: {\n          iconTheme: {\n            primary: '#10b981',\n            secondary: '#fff',\n          },\n          style: {\n            borderLeft: '4px solid #10b981',\n          },\n        },\n        error: {\n          iconTheme: {\n            primary: '#ef4444',\n            secondary: '#fff',\n          },\n          style: {\n            borderLeft: '4px solid #ef4444',\n          },\n        },\n        loading: {\n          iconTheme: {\n            primary: '#3b82f6',\n            secondary: '#fff',\n          },\n          style: {\n            borderLeft: '4px solid #3b82f6',\n          },\n        },\n      }}\n    />\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBACE,6LAAC,0JAAA,CAAA,UAAO;QACN,UAAS;QACT,cAAc;YACZ,UAAU;YACV,OAAO;gBACL,YAAY;gBACZ,OAAO;gBACP,cAAc;gBACd,QAAQ;gBACR,WAAW;gBACX,SAAS;gBACT,UAAU;gBACV,YAAY;YACd;YACA,SAAS;gBACP,WAAW;oBACT,SAAS;oBACT,WAAW;gBACb;gBACA,OAAO;oBACL,YAAY;gBACd;YACF;YACA,OAAO;gBACL,WAAW;oBACT,SAAS;oBACT,WAAW;gBACb;gBACA,OAAO;oBACL,YAAY;gBACd;YACF;YACA,SAAS;gBACP,WAAW;oBACT,SAAS;oBACT,WAAW;gBACb;gBACA,OAAO;oBACL,YAAY;gBACd;YACF;QACF;;;;;;AAGN;KA9CwB", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/lib/capacitor/index.ts"], "sourcesContent": ["import { Capacitor } from '@capacitor/core'\nimport { StatusBar, Style } from '@capacitor/status-bar'\nimport { SplashScreen } from '@capacitor/splash-screen'\nimport { Haptics, ImpactStyle } from '@capacitor/haptics'\nimport { Toast } from '@capacitor/toast'\nimport { Share } from '@capacitor/share'\nimport { Network } from '@capacitor/network'\nimport { Device } from '@capacitor/device'\n\nexport class CapacitorService {\n  private static instance: CapacitorService\n  private isNative: boolean\n\n  constructor() {\n    this.isNative = Capacitor.isNativePlatform()\n  }\n\n  static getInstance(): CapacitorService {\n    if (!CapacitorService.instance) {\n      CapacitorService.instance = new CapacitorService()\n    }\n    return CapacitorService.instance\n  }\n\n  // Platform detection\n  isNativePlatform(): boolean {\n    return this.isNative\n  }\n\n  getPlatform(): string {\n    return Capacitor.getPlatform()\n  }\n\n  // App lifecycle\n  async initializeApp(): Promise<void> {\n    if (!this.isNative) return\n\n    try {\n      // Configure status bar\n      await StatusBar.setStyle({ style: Style.Dark })\n      await StatusBar.setBackgroundColor({ color: '#3b82f6' })\n\n      // Hide splash screen after app is ready\n      await SplashScreen.hide()\n\n      console.log('Capacitor app initialized successfully')\n    } catch (error) {\n      console.error('Error initializing Capacitor app:', error)\n    }\n  }\n\n  // Haptic feedback\n  async hapticImpact(style: ImpactStyle = ImpactStyle.Medium): Promise<void> {\n    if (!this.isNative) return\n\n    try {\n      await Haptics.impact({ style })\n    } catch (error) {\n      console.error('Error with haptic feedback:', error)\n    }\n  }\n\n  async hapticNotification(type: 'success' | 'warning' | 'error' = 'success'): Promise<void> {\n    if (!this.isNative) return\n\n    try {\n      await Haptics.notification({ \n        type: type === 'success' ? 'SUCCESS' : type === 'warning' ? 'WARNING' : 'ERROR' \n      })\n    } catch (error) {\n      console.error('Error with haptic notification:', error)\n    }\n  }\n\n  // Toast notifications\n  async showToast(message: string, duration: 'short' | 'long' = 'short'): Promise<void> {\n    if (!this.isNative) {\n      // Fallback for web\n      console.log('Toast:', message)\n      return\n    }\n\n    try {\n      await Toast.show({\n        text: message,\n        duration: duration\n      })\n    } catch (error) {\n      console.error('Error showing toast:', error)\n    }\n  }\n\n  // Sharing\n  async share(options: {\n    title?: string\n    text?: string\n    url?: string\n    dialogTitle?: string\n  }): Promise<void> {\n    if (!this.isNative) {\n      // Fallback for web\n      if (navigator.share) {\n        try {\n          await navigator.share(options)\n        } catch (error) {\n          console.error('Error sharing:', error)\n        }\n      } else {\n        console.log('Share not supported on this platform')\n      }\n      return\n    }\n\n    try {\n      await Share.share(options)\n    } catch (error) {\n      console.error('Error sharing:', error)\n    }\n  }\n\n  // Network status\n  async getNetworkStatus(): Promise<{ connected: boolean; connectionType: string }> {\n    try {\n      const status = await Network.getStatus()\n      return {\n        connected: status.connected,\n        connectionType: status.connectionType\n      }\n    } catch (error) {\n      console.error('Error getting network status:', error)\n      return { connected: true, connectionType: 'unknown' }\n    }\n  }\n\n  // Device information\n  async getDeviceInfo(): Promise<{\n    platform: string\n    model: string\n    osVersion: string\n    manufacturer: string\n  }> {\n    try {\n      const info = await Device.getInfo()\n      return {\n        platform: info.platform,\n        model: info.model,\n        osVersion: info.osVersion,\n        manufacturer: info.manufacturer\n      }\n    } catch (error) {\n      console.error('Error getting device info:', error)\n      return {\n        platform: 'unknown',\n        model: 'unknown',\n        osVersion: 'unknown',\n        manufacturer: 'unknown'\n      }\n    }\n  }\n\n  // Status bar management\n  async setStatusBarStyle(style: 'light' | 'dark'): Promise<void> {\n    if (!this.isNative) return\n\n    try {\n      await StatusBar.setStyle({ \n        style: style === 'light' ? Style.Light : Style.Dark \n      })\n    } catch (error) {\n      console.error('Error setting status bar style:', error)\n    }\n  }\n\n  async hideStatusBar(): Promise<void> {\n    if (!this.isNative) return\n\n    try {\n      await StatusBar.hide()\n    } catch (error) {\n      console.error('Error hiding status bar:', error)\n    }\n  }\n\n  async showStatusBar(): Promise<void> {\n    if (!this.isNative) return\n\n    try {\n      await StatusBar.show()\n    } catch (error) {\n      console.error('Error showing status bar:', error)\n    }\n  }\n}\n\n// Export singleton instance\nexport const capacitorService = CapacitorService.getInstance()\n\n// React hook for using Capacitor features\nimport { useState, useEffect } from 'react'\n\nexport function useCapacitor() {\n  const [isNative, setIsNative] = useState(false)\n  const [networkStatus, setNetworkStatus] = useState({ connected: true, connectionType: 'unknown' })\n\n  useEffect(() => {\n    setIsNative(capacitorService.isNativePlatform())\n\n    // Initialize app if native\n    if (capacitorService.isNativePlatform()) {\n      capacitorService.initializeApp()\n    }\n\n    // Monitor network status\n    const updateNetworkStatus = async () => {\n      const status = await capacitorService.getNetworkStatus()\n      setNetworkStatus(status)\n    }\n\n    updateNetworkStatus()\n\n    // Set up network listener if native\n    if (capacitorService.isNativePlatform()) {\n      const networkListener = Network.addListener('networkStatusChange', (status) => {\n        setNetworkStatus({\n          connected: status.connected,\n          connectionType: status.connectionType\n        })\n      })\n\n      return () => {\n        networkListener.remove()\n      }\n    }\n  }, [])\n\n  return {\n    isNative,\n    networkStatus,\n    hapticImpact: capacitorService.hapticImpact.bind(capacitorService),\n    hapticNotification: capacitorService.hapticNotification.bind(capacitorService),\n    showToast: capacitorService.showToast.bind(capacitorService),\n    share: capacitorService.share.bind(capacitorService),\n    setStatusBarStyle: capacitorService.setStatusBarStyle.bind(capacitorService)\n  }\n}\n\n// Utility functions\nexport function isMobile(): boolean {\n  return capacitorService.isNativePlatform() || \n    (typeof window !== 'undefined' && window.innerWidth <= 768)\n}\n\nexport function isAndroid(): boolean {\n  return capacitorService.getPlatform() === 'android'\n}\n\nexport function isIOS(): boolean {\n  return capacitorService.getPlatform() === 'ios'\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AA8LA,0CAA0C;AAC1C;;;;;;;;;;AA7LO,MAAM;IACX,OAAe,SAA0B;IACjC,SAAiB;IAEzB,aAAc;QACZ,IAAI,CAAC,QAAQ,GAAG,uJAAA,CAAA,YAAS,CAAC,gBAAgB;IAC5C;IAEA,OAAO,cAAgC;QACrC,IAAI,CAAC,iBAAiB,QAAQ,EAAE;YAC9B,iBAAiB,QAAQ,GAAG,IAAI;QAClC;QACA,OAAO,iBAAiB,QAAQ;IAClC;IAEA,qBAAqB;IACrB,mBAA4B;QAC1B,OAAO,IAAI,CAAC,QAAQ;IACtB;IAEA,cAAsB;QACpB,OAAO,uJAAA,CAAA,YAAS,CAAC,WAAW;IAC9B;IAEA,gBAAgB;IAChB,MAAM,gBAA+B;QACnC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;QAEpB,IAAI;YACF,uBAAuB;YACvB,MAAM,uLAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;gBAAE,OAAO,6KAAA,CAAA,QAAK,CAAC,IAAI;YAAC;YAC7C,MAAM,uLAAA,CAAA,YAAS,CAAC,kBAAkB,CAAC;gBAAE,OAAO;YAAU;YAEtD,wCAAwC;YACxC,MAAM,0LAAA,CAAA,eAAY,CAAC,IAAI;YAEvB,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;QACrD;IACF;IAEA,kBAAkB;IAClB,MAAM,aAAa,QAAqB,uKAAA,CAAA,cAAW,CAAC,MAAM,EAAiB;QACzE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;QAEpB,IAAI;YACF,MAAM,iLAAA,CAAA,UAAO,CAAC,MAAM,CAAC;gBAAE;YAAM;QAC/B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;IACF;IAEA,MAAM,mBAAmB,OAAwC,SAAS,EAAiB;QACzF,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;QAEpB,IAAI;YACF,MAAM,iLAAA,CAAA,UAAO,CAAC,YAAY,CAAC;gBACzB,MAAM,SAAS,YAAY,YAAY,SAAS,YAAY,YAAY;YAC1E;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACnD;IACF;IAEA,sBAAsB;IACtB,MAAM,UAAU,OAAe,EAAE,WAA6B,OAAO,EAAiB;QACpF,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,mBAAmB;YACnB,QAAQ,GAAG,CAAC,UAAU;YACtB;QACF;QAEA,IAAI;YACF,MAAM,+KAAA,CAAA,QAAK,CAAC,IAAI,CAAC;gBACf,MAAM;gBACN,UAAU;YACZ;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,UAAU;IACV,MAAM,MAAM,OAKX,EAAiB;QAChB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,mBAAmB;YACnB,IAAI,UAAU,KAAK,EAAE;gBACnB,IAAI;oBACF,MAAM,UAAU,KAAK,CAAC;gBACxB,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,kBAAkB;gBAClC;YACF,OAAO;gBACL,QAAQ,GAAG,CAAC;YACd;YACA;QACF;QAEA,IAAI;YACF,MAAM,+KAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;QAClC;IACF;IAEA,iBAAiB;IACjB,MAAM,mBAA4E;QAChF,IAAI;YACF,MAAM,SAAS,MAAM,iLAAA,CAAA,UAAO,CAAC,SAAS;YACtC,OAAO;gBACL,WAAW,OAAO,SAAS;gBAC3B,gBAAgB,OAAO,cAAc;YACvC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO;gBAAE,WAAW;gBAAM,gBAAgB;YAAU;QACtD;IACF;IAEA,qBAAqB;IACrB,MAAM,gBAKH;QACD,IAAI;YACF,MAAM,OAAO,MAAM,gLAAA,CAAA,SAAM,CAAC,OAAO;YACjC,OAAO;gBACL,UAAU,KAAK,QAAQ;gBACvB,OAAO,KAAK,KAAK;gBACjB,WAAW,KAAK,SAAS;gBACzB,cAAc,KAAK,YAAY;YACjC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO;gBACL,UAAU;gBACV,OAAO;gBACP,WAAW;gBACX,cAAc;YAChB;QACF;IACF;IAEA,wBAAwB;IACxB,MAAM,kBAAkB,KAAuB,EAAiB;QAC9D,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;QAEpB,IAAI;YACF,MAAM,uLAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;gBACvB,OAAO,UAAU,UAAU,6KAAA,CAAA,QAAK,CAAC,KAAK,GAAG,6KAAA,CAAA,QAAK,CAAC,IAAI;YACrD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACnD;IACF;IAEA,MAAM,gBAA+B;QACnC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;QAEpB,IAAI;YACF,MAAM,uLAAA,CAAA,YAAS,CAAC,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;IACF;IAEA,MAAM,gBAA+B;QACnC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;QAEpB,IAAI;YACF,MAAM,uLAAA,CAAA,YAAS,CAAC,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;AACF;AAGO,MAAM,mBAAmB,iBAAiB,WAAW;;AAKrD,SAAS;;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,WAAW;QAAM,gBAAgB;IAAU;IAEhG,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,YAAY,iBAAiB,gBAAgB;YAE7C,2BAA2B;YAC3B,IAAI,iBAAiB,gBAAgB,IAAI;gBACvC,iBAAiB,aAAa;YAChC;YAEA,yBAAyB;YACzB,MAAM;8DAAsB;oBAC1B,MAAM,SAAS,MAAM,iBAAiB,gBAAgB;oBACtD,iBAAiB;gBACnB;;YAEA;YAEA,oCAAoC;YACpC,IAAI,iBAAiB,gBAAgB,IAAI;gBACvC,MAAM,kBAAkB,iLAAA,CAAA,UAAO,CAAC,WAAW,CAAC;8DAAuB,CAAC;wBAClE,iBAAiB;4BACf,WAAW,OAAO,SAAS;4BAC3B,gBAAgB,OAAO,cAAc;wBACvC;oBACF;;gBAEA;8CAAO;wBACL,gBAAgB,MAAM;oBACxB;;YACF;QACF;iCAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA,cAAc,iBAAiB,YAAY,CAAC,IAAI,CAAC;QACjD,oBAAoB,iBAAiB,kBAAkB,CAAC,IAAI,CAAC;QAC7D,WAAW,iBAAiB,SAAS,CAAC,IAAI,CAAC;QAC3C,OAAO,iBAAiB,KAAK,CAAC,IAAI,CAAC;QACnC,mBAAmB,iBAAiB,iBAAiB,CAAC,IAAI,CAAC;IAC7D;AACF;GA5CgB;AA+CT,SAAS;IACd,OAAO,iBAAiB,gBAAgB,MACrC,aAAkB,eAAe,OAAO,UAAU,IAAI;AAC3D;AAEO,SAAS;IACd,OAAO,iBAAiB,WAAW,OAAO;AAC5C;AAEO,SAAS;IACd,OAAO,iBAAiB,WAAW,OAAO;AAC5C", "debugId": null}}, {"offset": {"line": 342, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/mobile/MobileLayout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { useCapacitor, isMobile } from '@/lib/capacitor'\nimport { \n  HomeIcon, \n  CheckSquareIcon, \n  DollarSignIcon, \n  ShoppingCartIcon, \n  MessageSquareIcon, \n  ChefHatIcon,\n  CalendarIcon,\n  MenuIcon,\n  XIcon,\n  WifiOffIcon\n} from 'lucide-react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\n\ninterface MobileLayoutProps {\n  children: React.ReactNode\n}\n\nconst mobileNavItems = [\n  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },\n  { name: 'Tasks', href: '/tasks', icon: CheckSquareIcon },\n  { name: 'Budget', href: '/budget', icon: DollarSignIcon },\n  { name: 'Shopping', href: '/shopping', icon: ShoppingCartIcon },\n  { name: 'Chat', href: '/chat', icon: MessageSquareIcon },\n  { name: 'Recipes', href: '/recipes', icon: ChefHatIcon },\n  { name: 'Meals', href: '/meal-plans', icon: CalendarIcon },\n]\n\nexport default function MobileLayout({ children }: MobileLayoutProps) {\n  const [showMobileMenu, setShowMobileMenu] = useState(false)\n  const { isNative, networkStatus, hapticImpact } = useCapacitor()\n  const pathname = usePathname()\n  const mobile = isMobile()\n\n  // Don't render mobile layout if not on mobile\n  if (!mobile) {\n    return <>{children}</>\n  }\n\n  const handleNavClick = async (href: string) => {\n    if (isNative) {\n      await hapticImpact()\n    }\n    setShowMobileMenu(false)\n  }\n\n  const currentNavItem = mobileNavItems.find(item => pathname.startsWith(item.href))\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex flex-col\">\n      {/* Status Bar Spacer for iOS */}\n      {isNative && (\n        <div className=\"h-safe-top bg-blue-600\"></div>\n      )}\n\n      {/* Top Header */}\n      <header className=\"bg-blue-600 text-white shadow-lg\">\n        <div className=\"flex items-center justify-between px-4 py-3\">\n          <div className=\"flex items-center space-x-3\">\n            <button\n              onClick={() => {\n                setShowMobileMenu(!showMobileMenu)\n                if (isNative) hapticImpact()\n              }}\n              className=\"p-2 rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              {showMobileMenu ? (\n                <XIcon className=\"h-6 w-6\" />\n              ) : (\n                <MenuIcon className=\"h-6 w-6\" />\n              )}\n            </button>\n            <h1 className=\"text-lg font-semibold\">\n              {currentNavItem?.name || 'LifeManager'}\n            </h1>\n          </div>\n\n          {/* Network Status Indicator */}\n          {!networkStatus.connected && (\n            <div className=\"flex items-center space-x-1 text-yellow-200\">\n              <WifiOffIcon className=\"h-4 w-4\" />\n              <span className=\"text-xs\">Offline</span>\n            </div>\n          )}\n        </div>\n      </header>\n\n      {/* Mobile Menu Overlay */}\n      <AnimatePresence>\n        {showMobileMenu && (\n          <>\n            {/* Backdrop */}\n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              exit={{ opacity: 0 }}\n              className=\"fixed inset-0 bg-black bg-opacity-50 z-40\"\n              onClick={() => setShowMobileMenu(false)}\n            />\n\n            {/* Menu Panel */}\n            <motion.div\n              initial={{ x: '-100%' }}\n              animate={{ x: 0 }}\n              exit={{ x: '-100%' }}\n              transition={{ type: 'spring', damping: 25, stiffness: 200 }}\n              className=\"fixed left-0 top-0 bottom-0 w-80 bg-white shadow-xl z-50 flex flex-col\"\n            >\n              {/* Menu Header */}\n              <div className=\"bg-blue-600 text-white p-4\">\n                <div className=\"flex items-center justify-between\">\n                  <h2 className=\"text-xl font-bold\">LifeManager</h2>\n                  <button\n                    onClick={() => setShowMobileMenu(false)}\n                    className=\"p-2 rounded-lg hover:bg-blue-700 transition-colors\"\n                  >\n                    <XIcon className=\"h-5 w-5\" />\n                  </button>\n                </div>\n              </div>\n\n              {/* Navigation Items */}\n              <nav className=\"flex-1 py-4\">\n                {mobileNavItems.map((item) => {\n                  const isActive = pathname.startsWith(item.href)\n                  return (\n                    <Link\n                      key={item.name}\n                      href={item.href}\n                      onClick={() => handleNavClick(item.href)}\n                      className={`flex items-center space-x-3 px-6 py-4 text-gray-700 hover:bg-gray-100 transition-colors ${\n                        isActive ? 'bg-blue-50 text-blue-600 border-r-4 border-blue-600' : ''\n                      }`}\n                    >\n                      <item.icon className={`h-6 w-6 ${isActive ? 'text-blue-600' : 'text-gray-500'}`} />\n                      <span className=\"font-medium\">{item.name}</span>\n                    </Link>\n                  )\n                })}\n              </nav>\n\n              {/* Menu Footer */}\n              <div className=\"p-4 border-t border-gray-200\">\n                <div className=\"text-xs text-gray-500 text-center\">\n                  {isNative ? 'Mobile App' : 'Web App'} • v1.0.0\n                </div>\n              </div>\n            </motion.div>\n          </>\n        )}\n      </AnimatePresence>\n\n      {/* Main Content */}\n      <main className=\"flex-1 overflow-auto\">\n        <div className=\"p-4\">\n          {children}\n        </div>\n      </main>\n\n      {/* Bottom Navigation */}\n      <nav className=\"bg-white border-t border-gray-200 px-2 py-1 safe-bottom\">\n        <div className=\"flex items-center justify-around\">\n          {mobileNavItems.slice(0, 5).map((item) => {\n            const isActive = pathname.startsWith(item.href)\n            return (\n              <Link\n                key={item.name}\n                href={item.href}\n                onClick={() => handleNavClick(item.href)}\n                className={`flex flex-col items-center space-y-1 px-3 py-2 rounded-lg transition-colors ${\n                  isActive \n                    ? 'text-blue-600 bg-blue-50' \n                    : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'\n                }`}\n              >\n                <item.icon className=\"h-5 w-5\" />\n                <span className=\"text-xs font-medium\">{item.name}</span>\n              </Link>\n            )\n          })}\n        </div>\n      </nav>\n\n      {/* Safe Area Bottom Spacer for iOS */}\n      {isNative && (\n        <div className=\"h-safe-bottom bg-white\"></div>\n      )}\n    </div>\n  )\n}\n\n// Hook for mobile-specific features\nexport function useMobileFeatures() {\n  const { isNative, hapticImpact, hapticNotification, showToast, share } = useCapacitor()\n  const [isOnline, setIsOnline] = useState(true)\n\n  useEffect(() => {\n    const handleOnline = () => setIsOnline(true)\n    const handleOffline = () => setIsOnline(false)\n\n    window.addEventListener('online', handleOnline)\n    window.addEventListener('offline', handleOffline)\n\n    return () => {\n      window.removeEventListener('online', handleOnline)\n      window.removeEventListener('offline', handleOffline)\n    }\n  }, [])\n\n  const showSuccessToast = async (message: string) => {\n    if (isNative) {\n      await hapticNotification('success')\n      await showToast(message)\n    }\n  }\n\n  const showErrorToast = async (message: string) => {\n    if (isNative) {\n      await hapticNotification('error')\n      await showToast(message)\n    }\n  }\n\n  const shareContent = async (content: { title?: string; text?: string; url?: string }) => {\n    await share(content)\n  }\n\n  return {\n    isNative,\n    isOnline,\n    hapticImpact,\n    showSuccessToast,\n    showErrorToast,\n    shareContent\n  }\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;;;AAlBA;;;;;;;AAwBA,MAAM,iBAAiB;IACrB;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,0MAAA,CAAA,WAAQ;IAAC;IACxD;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,kOAAA,CAAA,kBAAe;IAAC;IACvD;QAAE,MAAM;QAAU,MAAM;QAAW,MAAM,yNAAA,CAAA,iBAAc;IAAC;IACxD;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,6NAAA,CAAA,mBAAgB;IAAC;IAC9D;QAAE,MAAM;QAAQ,MAAM;QAAS,MAAM,+NAAA,CAAA,oBAAiB;IAAC;IACvD;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,mNAAA,CAAA,cAAW;IAAC;IACvD;QAAE,MAAM;QAAS,MAAM;QAAe,MAAM,iNAAA,CAAA,eAAY;IAAC;CAC1D;AAEc,SAAS,aAAa,EAAE,QAAQ,EAAqB;;IAClE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAC7D,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IAEtB,8CAA8C;IAC9C,IAAI,CAAC,QAAQ;QACX,qBAAO;sBAAG;;IACZ;IAEA,MAAM,iBAAiB,OAAO;QAC5B,IAAI,UAAU;YACZ,MAAM;QACR;QACA,kBAAkB;IACpB;IAEA,MAAM,iBAAiB,eAAe,IAAI,CAAC,CAAA,OAAQ,SAAS,UAAU,CAAC,KAAK,IAAI;IAEhF,qBACE,6LAAC;QAAI,WAAU;;YAEZ,0BACC,6LAAC;gBAAI,WAAU;;;;;;0BAIjB,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;wCACP,kBAAkB,CAAC;wCACnB,IAAI,UAAU;oCAChB;oCACA,WAAU;8CAET,+BACC,6LAAC,mMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;6DAEjB,6LAAC,yMAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAGxB,6LAAC;oCAAG,WAAU;8CACX,gBAAgB,QAAQ;;;;;;;;;;;;wBAK5B,CAAC,cAAc,SAAS,kBACvB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,mNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,6LAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;;;;;;;;;;;;0BAOlC,6LAAC,4LAAA,CAAA,kBAAe;0BACb,gCACC;;sCAEE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,MAAM;gCAAE,SAAS;4BAAE;4BACnB,WAAU;4BACV,SAAS,IAAM,kBAAkB;;;;;;sCAInC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,GAAG;4BAAQ;4BACtB,SAAS;gCAAE,GAAG;4BAAE;4BAChB,MAAM;gCAAE,GAAG;4BAAQ;4BACnB,YAAY;gCAAE,MAAM;gCAAU,SAAS;gCAAI,WAAW;4BAAI;4BAC1D,WAAU;;8CAGV,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAoB;;;;;;0DAClC,6LAAC;gDACC,SAAS,IAAM,kBAAkB;gDACjC,WAAU;0DAEV,cAAA,6LAAC,mMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8CAMvB,6LAAC;oCAAI,WAAU;8CACZ,eAAe,GAAG,CAAC,CAAC;wCACnB,MAAM,WAAW,SAAS,UAAU,CAAC,KAAK,IAAI;wCAC9C,qBACE,6LAAC,+JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,SAAS,IAAM,eAAe,KAAK,IAAI;4CACvC,WAAW,CAAC,wFAAwF,EAClG,WAAW,wDAAwD,IACnE;;8DAEF,6LAAC,KAAK,IAAI;oDAAC,WAAW,CAAC,QAAQ,EAAE,WAAW,kBAAkB,iBAAiB;;;;;;8DAC/E,6LAAC;oDAAK,WAAU;8DAAe,KAAK,IAAI;;;;;;;2CARnC,KAAK,IAAI;;;;;oCAWpB;;;;;;8CAIF,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;4CACZ,WAAW,eAAe;4CAAU;;;;;;;;;;;;;;;;;;;;;;;;;0BASjD,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;0BAKL,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACZ,eAAe,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;wBAC/B,MAAM,WAAW,SAAS,UAAU,CAAC,KAAK,IAAI;wBAC9C,qBACE,6LAAC,+JAAA,CAAA,UAAI;4BAEH,MAAM,KAAK,IAAI;4BACf,SAAS,IAAM,eAAe,KAAK,IAAI;4BACvC,WAAW,CAAC,4EAA4E,EACtF,WACI,6BACA,sDACJ;;8CAEF,6LAAC,KAAK,IAAI;oCAAC,WAAU;;;;;;8CACrB,6LAAC;oCAAK,WAAU;8CAAuB,KAAK,IAAI;;;;;;;2BAV3C,KAAK,IAAI;;;;;oBAapB;;;;;;;;;;;YAKH,0BACC,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAIvB;GAjKwB;;QAE4B,mIAAA,CAAA,eAAY;QAC7C,qIAAA,CAAA,cAAW;;;KAHN;AAoKjB,SAAS;;IACd,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,kBAAkB,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IACpF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM;4DAAe,IAAM,YAAY;;YACvC,MAAM;6DAAgB,IAAM,YAAY;;YAExC,OAAO,gBAAgB,CAAC,UAAU;YAClC,OAAO,gBAAgB,CAAC,WAAW;YAEnC;+CAAO;oBACL,OAAO,mBAAmB,CAAC,UAAU;oBACrC,OAAO,mBAAmB,CAAC,WAAW;gBACxC;;QACF;sCAAG,EAAE;IAEL,MAAM,mBAAmB,OAAO;QAC9B,IAAI,UAAU;YACZ,MAAM,mBAAmB;YACzB,MAAM,UAAU;QAClB;IACF;IAEA,MAAM,iBAAiB,OAAO;QAC5B,IAAI,UAAU;YACZ,MAAM,mBAAmB;YACzB,MAAM,UAAU;QAClB;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,MAAM,MAAM;IACd;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;IACF;AACF;IA3CgB;;QAC2D,mIAAA,CAAA,eAAY", "debugId": null}}, {"offset": {"line": 803, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/lib/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\n\nexport function createClient() {\n  return createBrowserClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n  )\n}\n"], "names": [], "mappings": ";;;AAII;AAJJ;AAAA;;AAEO,SAAS;IACd,OAAO,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD;AAI3B", "debugId": null}}, {"offset": {"line": 822, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { createClient } from '@/lib/supabase/client'\nimport { useRouter } from 'next/navigation'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { useSidebar } from '@/components/layout/SidebarContext'\nimport {\n  HomeIcon,\n  CheckSquareIcon,\n  DollarSignIcon,\n  ShoppingCartIcon,\n  MessageCircleIcon,\n  ChefHatIcon,\n  LogOutIcon,\n  MenuIcon,\n  XIcon,\n  UserIcon,\n  SettingsIcon,\n  BellIcon,\n  SparklesIcon,\n  ChevronLeftIcon,\n  ChevronRightIcon,\n  TrendingUpIcon,\n  TargetIcon,\n  CalendarIcon,\n} from 'lucide-react'\n\nconst getNavigation = (stats: any) => [\n  {\n    name: 'Dashboard',\n    href: '/dashboard',\n    icon: HomeIcon,\n    color: 'text-blue-600',\n    badge: null,\n    description: 'Overview & Analytics'\n  },\n  {\n    name: 'Tasks',\n    href: '/tasks',\n    icon: CheckSquareIcon,\n    color: 'text-green-600',\n    badge: stats.tasksToday,\n    description: 'Manage your tasks'\n  },\n  {\n    name: 'Budget',\n    href: '/budget',\n    icon: DollarSignIcon,\n    color: 'text-emerald-600',\n    badge: stats.budgetAlert ? '!' : null,\n    description: 'Track expenses'\n  },\n  {\n    name: 'Shopping Lists',\n    href: '/shopping',\n    icon: ShoppingCartIcon,\n    color: 'text-purple-600',\n    badge: null,\n    description: 'Shopping & Lists'\n  },\n  {\n    name: 'AI Chat',\n    href: '/chat',\n    icon: MessageCircleIcon,\n    color: 'text-indigo-600',\n    badge: null,\n    description: 'AI Assistant'\n  },\n  {\n    name: 'Recipes',\n    href: '/recipes',\n    icon: ChefHatIcon,\n    color: 'text-orange-600',\n    badge: null,\n    description: 'Recipe Collection'\n  },\n  {\n    name: 'Meal Plans',\n    href: '/meal-plans',\n    icon: CalendarIcon,\n    color: 'text-pink-600',\n    badge: null,\n    description: 'Weekly Meal Planning'\n  },\n]\n\nexport default function Sidebar() {\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n  const [isCollapsed, setIsCollapsed] = useState(false)\n  const [user, setUser] = useState<any>(null)\n  const [notifications, setNotifications] = useState(3)\n  const [quickStats, setQuickStats] = useState({\n    tasksToday: 5,\n    budgetAlert: true,\n    upcomingDeadlines: 2\n  })\n  const pathname = usePathname()\n  const router = useRouter()\n  const supabase = createClient()\n\n  useEffect(() => {\n    const getUser = async () => {\n      const { data: { user } } = await supabase.auth.getUser()\n      setUser(user)\n    }\n    getUser()\n\n    // Load sidebar state from localStorage\n    const savedCollapsed = localStorage.getItem('sidebar-collapsed')\n    if (savedCollapsed) {\n      setIsCollapsed(JSON.parse(savedCollapsed))\n    }\n  }, [])\n\n  const toggleCollapsed = () => {\n    const newState = !isCollapsed\n    setIsCollapsed(newState)\n    localStorage.setItem('sidebar-collapsed', JSON.stringify(newState))\n  }\n\n  const handleLogout = async () => {\n    await supabase.auth.signOut()\n    router.push('/login')\n    router.refresh()\n  }\n\n  const getUserInitials = (email: string) => {\n    return email.split('@')[0].slice(0, 2).toUpperCase()\n  }\n\n  const navigation = getNavigation(quickStats)\n\n  return (\n    <>\n      {/* Mobile sidebar */}\n      <div className={`fixed inset-0 flex z-40 md:hidden ${sidebarOpen ? '' : 'hidden'}`}>\n        <div className=\"fixed inset-0 bg-gray-900 bg-opacity-75 transition-opacity\" onClick={() => setSidebarOpen(false)} />\n        <div className=\"relative flex-1 flex flex-col max-w-xs w-full bg-white shadow-xl\">\n          <div className=\"absolute top-0 right-0 -mr-12 pt-2\">\n            <button\n              type=\"button\"\n              className=\"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white hover:bg-gray-600 transition-colors\"\n              onClick={() => setSidebarOpen(false)}\n            >\n              <XIcon className=\"h-6 w-6 text-white\" />\n            </button>\n          </div>\n\n          {/* Header with logo and user */}\n          <div className=\"flex-shrink-0 px-4 py-6 bg-gradient-to-r from-blue-600 to-indigo-600\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"flex-shrink-0\">\n                  <SparklesIcon className=\"h-8 w-8 text-white\" />\n                </div>\n                <h1 className=\"text-xl font-bold text-white\">LifeManager</h1>\n              </div>\n            </div>\n            {user && (\n              <div className=\"mt-4 flex items-center space-x-3\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"h-10 w-10 rounded-full bg-white bg-opacity-20 flex items-center justify-center\">\n                    <span className=\"text-sm font-medium text-white\">\n                      {getUserInitials(user.email)}\n                    </span>\n                  </div>\n                </div>\n                <div className=\"min-w-0 flex-1\">\n                  <p className=\"text-sm font-medium text-white truncate\">\n                    {user.email}\n                  </p>\n                  <p className=\"text-xs text-blue-100\">\n                    Welcome back!\n                  </p>\n                </div>\n              </div>\n            )}\n          </div>\n\n          <div className=\"flex-1 h-0 pt-5 pb-4 overflow-y-auto\">\n            <nav className=\"px-3 space-y-1\" role=\"navigation\" aria-label=\"Main navigation\">\n              {navigation.map((item) => {\n                const isActive = pathname === item.href\n                return (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    onClick={() => setSidebarOpen(false)}\n                    className={`group flex items-center px-3 py-3 text-base font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${\n                      isActive\n                        ? 'bg-blue-50 text-blue-700 border-r-4 border-blue-600'\n                        : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'\n                    }`}\n                    aria-current={isActive ? 'page' : undefined}\n                  >\n                    <item.icon\n                      className={`mr-4 h-6 w-6 transition-colors ${\n                        isActive ? 'text-blue-600' : `${item.color} group-hover:${item.color}`\n                      }`}\n                      aria-hidden=\"true\"\n                    />\n                    {item.name}\n                  </Link>\n                )\n              })}\n            </nav>\n          </div>\n\n          <div className=\"flex-shrink-0 border-t border-gray-200 p-4\">\n            <button\n              onClick={handleLogout}\n              className=\"w-full flex items-center px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors\"\n            >\n              <LogOutIcon className=\"mr-3 h-5 w-5\" />\n              Logout\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <motion.div\n        className=\"hidden md:flex md:flex-col md:fixed md:inset-y-0 z-30\"\n        animate={{ width: isCollapsed ? 80 : 280 }}\n        transition={{ duration: 0.3, ease: \"easeInOut\" }}\n      >\n        <div className=\"flex-1 flex flex-col min-h-0 bg-white shadow-xl border-r border-gray-200\">\n          {/* Header with logo and user */}\n          <div className=\"flex-shrink-0 px-4 py-6 bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-600 relative overflow-hidden\">\n            <div className=\"absolute inset-0 bg-gradient-to-r from-blue-600/20 to-purple-600/20 backdrop-blur-sm\"></div>\n            <div className=\"relative\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center space-x-3\">\n                  <motion.div\n                    animate={{ scale: isCollapsed ? 1.2 : 1 }}\n                    transition={{ duration: 0.3 }}\n                    className=\"flex-shrink-0\"\n                  >\n                    <SparklesIcon className=\"h-8 w-8 text-white\" />\n                  </motion.div>\n                  <AnimatePresence>\n                    {!isCollapsed && (\n                      <motion.h1\n                        initial={{ opacity: 0, x: -20 }}\n                        animate={{ opacity: 1, x: 0 }}\n                        exit={{ opacity: 0, x: -20 }}\n                        transition={{ duration: 0.2 }}\n                        className=\"text-xl font-bold text-white\"\n                      >\n                        LifeManager\n                      </motion.h1>\n                    )}\n                  </AnimatePresence>\n                </div>\n\n                <motion.button\n                  onClick={toggleCollapsed}\n                  className=\"p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors\"\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  {isCollapsed ? (\n                    <ChevronRightIcon className=\"h-4 w-4 text-white\" />\n                  ) : (\n                    <ChevronLeftIcon className=\"h-4 w-4 text-white\" />\n                  )}\n                </motion.button>\n              </div>\n\n              <AnimatePresence>\n                {user && !isCollapsed && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    exit={{ opacity: 0, y: -20 }}\n                    transition={{ duration: 0.3, delay: 0.1 }}\n                    className=\"mt-4 flex items-center space-x-3\"\n                  >\n                    <div className=\"flex-shrink-0\">\n                      <div className=\"h-10 w-10 rounded-full bg-white bg-opacity-20 flex items-center justify-center ring-2 ring-white/30\">\n                        <span className=\"text-sm font-medium text-white\">\n                          {getUserInitials(user.email)}\n                        </span>\n                      </div>\n                    </div>\n                    <div className=\"min-w-0 flex-1\">\n                      <p className=\"text-sm font-medium text-white truncate\">\n                        {user.email}\n                      </p>\n                      <p className=\"text-xs text-blue-100\">\n                        Welcome back! 👋\n                      </p>\n                    </div>\n                  </motion.div>\n                )}\n              </AnimatePresence>\n            </div>\n          </div>\n\n          {/* Quick Stats */}\n          <AnimatePresence>\n            {!isCollapsed && (\n              <motion.div\n                initial={{ opacity: 0, height: 0 }}\n                animate={{ opacity: 1, height: 'auto' }}\n                exit={{ opacity: 0, height: 0 }}\n                transition={{ duration: 0.3 }}\n                className=\"px-4 py-3 bg-gray-50 border-b border-gray-200\"\n              >\n                <div className=\"grid grid-cols-3 gap-2 text-center\">\n                  <div className=\"p-2 bg-white rounded-lg shadow-sm\">\n                    <div className=\"text-lg font-bold text-blue-600\">{quickStats.tasksToday}</div>\n                    <div className=\"text-xs text-gray-600\">Today</div>\n                  </div>\n                  <div className=\"p-2 bg-white rounded-lg shadow-sm\">\n                    <div className=\"text-lg font-bold text-green-600\">85%</div>\n                    <div className=\"text-xs text-gray-600\">Complete</div>\n                  </div>\n                  <div className=\"p-2 bg-white rounded-lg shadow-sm\">\n                    <div className=\"text-lg font-bold text-orange-600\">{quickStats.upcomingDeadlines}</div>\n                    <div className=\"text-xs text-gray-600\">Due</div>\n                  </div>\n                </div>\n              </motion.div>\n            )}\n          </AnimatePresence>\n\n          <div className=\"flex-1 flex flex-col pt-5 pb-4 overflow-y-auto\">\n            <nav className=\"flex-1 px-3 space-y-2\" role=\"navigation\" aria-label=\"Main navigation\">\n              {navigation.map((item, index) => {\n                const isActive = pathname === item.href\n                return (\n                  <motion.div\n                    key={item.name}\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ duration: 0.3, delay: index * 0.05 }}\n                  >\n                    <Link\n                      href={item.href}\n                      className={`group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 relative overflow-hidden ${\n                        isActive\n                          ? 'bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-700 shadow-md border border-blue-200'\n                          : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900 hover:shadow-sm'\n                      }`}\n                      aria-current={isActive ? 'page' : undefined}\n                      title={isCollapsed ? item.name : undefined}\n                    >\n                      {isActive && (\n                        <motion.div\n                          layoutId=\"activeTab\"\n                          className=\"absolute inset-0 bg-gradient-to-r from-blue-500/10 to-indigo-500/10\"\n                          transition={{ type: \"spring\", bounce: 0.2, duration: 0.6 }}\n                        />\n                      )}\n\n                      <div className=\"relative flex items-center w-full\">\n                        <motion.div\n                          whileHover={{ scale: 1.1 }}\n                          whileTap={{ scale: 0.95 }}\n                          className=\"flex-shrink-0\"\n                        >\n                          <item.icon\n                            className={`h-5 w-5 transition-colors ${\n                              isActive ? 'text-blue-600' : `${item.color} group-hover:${item.color}`\n                            }`}\n                            aria-hidden=\"true\"\n                          />\n                        </motion.div>\n\n                        <AnimatePresence>\n                          {!isCollapsed && (\n                            <motion.div\n                              initial={{ opacity: 0, x: -10 }}\n                              animate={{ opacity: 1, x: 0 }}\n                              exit={{ opacity: 0, x: -10 }}\n                              transition={{ duration: 0.2 }}\n                              className=\"ml-3 flex-1 flex items-center justify-between\"\n                            >\n                              <div>\n                                <div className=\"font-medium\">{item.name}</div>\n                                <div className=\"text-xs text-gray-500\">{item.description}</div>\n                              </div>\n\n                              {item.badge && (\n                                <motion.div\n                                  initial={{ scale: 0 }}\n                                  animate={{ scale: 1 }}\n                                  className={`ml-2 px-2 py-1 text-xs font-bold rounded-full ${\n                                    item.badge === '!'\n                                      ? 'bg-red-100 text-red-600'\n                                      : 'bg-blue-100 text-blue-600'\n                                  }`}\n                                >\n                                  {item.badge}\n                                </motion.div>\n                              )}\n                            </motion.div>\n                          )}\n                        </AnimatePresence>\n\n                        {isCollapsed && item.badge && (\n                          <motion.div\n                            initial={{ scale: 0 }}\n                            animate={{ scale: 1 }}\n                            className=\"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full\"\n                          />\n                        )}\n                      </div>\n                    </Link>\n                  </motion.div>\n                )\n              })}\n            </nav>\n          </div>\n\n          <div className=\"flex-shrink-0 border-t border-gray-200 p-4\">\n            <motion.button\n              onClick={handleLogout}\n              className={`w-full flex items-center px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors ${\n                isCollapsed ? 'justify-center' : ''\n              }`}\n              whileHover={{ scale: 1.02 }}\n              whileTap={{ scale: 0.98 }}\n              title={isCollapsed ? 'Logout' : undefined}\n            >\n              <LogOutIcon className=\"h-5 w-5\" />\n              <AnimatePresence>\n                {!isCollapsed && (\n                  <motion.span\n                    initial={{ opacity: 0, x: -10 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    exit={{ opacity: 0, x: -10 }}\n                    transition={{ duration: 0.2 }}\n                    className=\"ml-3\"\n                  >\n                    Logout\n                  </motion.span>\n                )}\n              </AnimatePresence>\n            </motion.button>\n          </div>\n        </div>\n      </motion.div>\n\n      {/* Mobile menu button */}\n      <div className=\"md:hidden\">\n        <div className=\"flex items-center justify-between bg-white border-b border-gray-200 px-4 py-3 shadow-sm sticky top-0 z-40\">\n          <div className=\"flex items-center space-x-2\">\n            <SparklesIcon className=\"h-6 w-6 text-blue-600\" />\n            <h1 className=\"text-lg font-semibold text-gray-900 truncate\">LifeManager</h1>\n          </div>\n          <div className=\"flex items-center space-x-1\">\n            {user && (\n              <div className=\"flex items-center space-x-2 mr-2\">\n                <div className=\"h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center\">\n                  <span className=\"text-xs font-medium text-blue-700\">\n                    {getUserInitials(user.email)}\n                  </span>\n                </div>\n              </div>\n            )}\n            <button\n              type=\"button\"\n              className=\"touch-target inline-flex items-center justify-center p-2 rounded-lg text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 transition-colors\"\n            >\n              <BellIcon className=\"h-5 w-5\" />\n            </button>\n            <button\n              type=\"button\"\n              className=\"touch-target inline-flex items-center justify-center p-2 rounded-lg text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 transition-colors\"\n              onClick={() => setSidebarOpen(true)}\n            >\n              <MenuIcon className=\"h-6 w-6\" />\n            </button>\n          </div>\n        </div>\n      </div>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AATA;;;;;;;;AA8BA,MAAM,gBAAgB,CAAC,QAAe;QACpC;YACE,MAAM;YACN,MAAM;YACN,MAAM,0MAAA,CAAA,WAAQ;YACd,OAAO;YACP,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,kOAAA,CAAA,kBAAe;YACrB,OAAO;YACP,OAAO,MAAM,UAAU;YACvB,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,yNAAA,CAAA,iBAAc;YACpB,OAAO;YACP,OAAO,MAAM,WAAW,GAAG,MAAM;YACjC,aAAa;QACf;QACA;YAC<PERSON>,MAAM;YACN,MAAM;YACN,MAAM,6NAAA,CAAA,mBAAgB;YACtB,OAAO;YACP,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,+NAAA,CAAA,oBAAiB;YACvB,OAAO;YACP,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,mNAAA,CAAA,cAAW;YACjB,OAAO;YACP,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,iNAAA,CAAA,eAAY;YAClB,OAAO;YACP,OAAO;YACP,aAAa;QACf;KACD;AAEc,SAAS;;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IACtC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,YAAY;QACZ,aAAa;QACb,mBAAmB;IACrB;IACA,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAE5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,MAAM;6CAAU;oBACd,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;oBACtD,QAAQ;gBACV;;YACA;YAEA,uCAAuC;YACvC,MAAM,iBAAiB,aAAa,OAAO,CAAC;YAC5C,IAAI,gBAAgB;gBAClB,eAAe,KAAK,KAAK,CAAC;YAC5B;QACF;4BAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB,MAAM,WAAW,CAAC;QAClB,eAAe;QACf,aAAa,OAAO,CAAC,qBAAqB,KAAK,SAAS,CAAC;IAC3D;IAEA,MAAM,eAAe;QACnB,MAAM,SAAS,IAAI,CAAC,OAAO;QAC3B,OAAO,IAAI,CAAC;QACZ,OAAO,OAAO;IAChB;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAO,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,GAAG,WAAW;IACpD;IAEA,MAAM,aAAa,cAAc;IAEjC,qBACE;;0BAEE,6LAAC;gBAAI,WAAW,CAAC,kCAAkC,EAAE,cAAc,KAAK,UAAU;;kCAChF,6LAAC;wBAAI,WAAU;wBAA6D,SAAS,IAAM,eAAe;;;;;;kCAC1G,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,eAAe;8CAE9B,cAAA,6LAAC,mMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAKrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,iNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;8DAE1B,6LAAC;oDAAG,WAAU;8DAA+B;;;;;;;;;;;;;;;;;oCAGhD,sBACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEACb,gBAAgB,KAAK,KAAK;;;;;;;;;;;;;;;;0DAIjC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEACV,KAAK,KAAK;;;;;;kEAEb,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;0CAQ7C,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;oCAAiB,MAAK;oCAAa,cAAW;8CAC1D,WAAW,GAAG,CAAC,CAAC;wCACf,MAAM,WAAW,aAAa,KAAK,IAAI;wCACvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,SAAS,IAAM,eAAe;4CAC9B,WAAW,CAAC,uKAAuK,EACjL,WACI,wDACA,sDACJ;4CACF,gBAAc,WAAW,SAAS;;8DAElC,6LAAC,KAAK,IAAI;oDACR,WAAW,CAAC,+BAA+B,EACzC,WAAW,kBAAkB,GAAG,KAAK,KAAK,CAAC,aAAa,EAAE,KAAK,KAAK,EAAE,EACtE;oDACF,eAAY;;;;;;gDAEb,KAAK,IAAI;;2CAhBL,KAAK,IAAI;;;;;oCAmBpB;;;;;;;;;;;0CAIJ,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,6LAAC,iNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;0BAQ/C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,OAAO,cAAc,KAAK;gBAAI;gBACzC,YAAY;oBAAE,UAAU;oBAAK,MAAM;gBAAY;0BAE/C,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,SAAS;gEAAE,OAAO,cAAc,MAAM;4DAAE;4DACxC,YAAY;gEAAE,UAAU;4DAAI;4DAC5B,WAAU;sEAEV,cAAA,6LAAC,iNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;;;;;;sEAE1B,6LAAC,4LAAA,CAAA,kBAAe;sEACb,CAAC,6BACA,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;gEACR,SAAS;oEAAE,SAAS;oEAAG,GAAG,CAAC;gEAAG;gEAC9B,SAAS;oEAAE,SAAS;oEAAG,GAAG;gEAAE;gEAC5B,MAAM;oEAAE,SAAS;oEAAG,GAAG,CAAC;gEAAG;gEAC3B,YAAY;oEAAE,UAAU;gEAAI;gEAC5B,WAAU;0EACX;;;;;;;;;;;;;;;;;8DAOP,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oDACZ,SAAS;oDACT,WAAU;oDACV,YAAY;wDAAE,OAAO;oDAAK;oDAC1B,UAAU;wDAAE,OAAO;oDAAK;8DAEvB,4BACC,6LAAC,6NAAA,CAAA,mBAAgB;wDAAC,WAAU;;;;;6EAE5B,6LAAC,2NAAA,CAAA,kBAAe;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAKjC,6LAAC,4LAAA,CAAA,kBAAe;sDACb,QAAQ,CAAC,6BACR,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,MAAM;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC3B,YAAY;oDAAE,UAAU;oDAAK,OAAO;gDAAI;gDACxC,WAAU;;kEAEV,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAU;0EACb,gBAAgB,KAAK,KAAK;;;;;;;;;;;;;;;;kEAIjC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EACV,KAAK,KAAK;;;;;;0EAEb,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAWjD,6LAAC,4LAAA,CAAA,kBAAe;sCACb,CAAC,6BACA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,QAAQ;gCAAE;gCACjC,SAAS;oCAAE,SAAS;oCAAG,QAAQ;gCAAO;gCACtC,MAAM;oCAAE,SAAS;oCAAG,QAAQ;gCAAE;gCAC9B,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAmC,WAAW,UAAU;;;;;;8DACvE,6LAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAmC;;;;;;8DAClD,6LAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAqC,WAAW,iBAAiB;;;;;;8DAChF,6LAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOjD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;gCAAwB,MAAK;gCAAa,cAAW;0CACjE,WAAW,GAAG,CAAC,CAAC,MAAM;oCACrB,MAAM,WAAW,aAAa,KAAK,IAAI;oCACvC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO,QAAQ;wCAAK;kDAEjD,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,WAAW,CAAC,8LAA8L,EACxM,WACI,8FACA,sEACJ;4CACF,gBAAc,WAAW,SAAS;4CAClC,OAAO,cAAc,KAAK,IAAI,GAAG;;gDAEhC,0BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,UAAS;oDACT,WAAU;oDACV,YAAY;wDAAE,MAAM;wDAAU,QAAQ;wDAAK,UAAU;oDAAI;;;;;;8DAI7D,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,YAAY;gEAAE,OAAO;4DAAI;4DACzB,UAAU;gEAAE,OAAO;4DAAK;4DACxB,WAAU;sEAEV,cAAA,6LAAC,KAAK,IAAI;gEACR,WAAW,CAAC,0BAA0B,EACpC,WAAW,kBAAkB,GAAG,KAAK,KAAK,CAAC,aAAa,EAAE,KAAK,KAAK,EAAE,EACtE;gEACF,eAAY;;;;;;;;;;;sEAIhB,6LAAC,4LAAA,CAAA,kBAAe;sEACb,CAAC,6BACA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gEACT,SAAS;oEAAE,SAAS;oEAAG,GAAG,CAAC;gEAAG;gEAC9B,SAAS;oEAAE,SAAS;oEAAG,GAAG;gEAAE;gEAC5B,MAAM;oEAAE,SAAS;oEAAG,GAAG,CAAC;gEAAG;gEAC3B,YAAY;oEAAE,UAAU;gEAAI;gEAC5B,WAAU;;kFAEV,6LAAC;;0FACC,6LAAC;gFAAI,WAAU;0FAAe,KAAK,IAAI;;;;;;0FACvC,6LAAC;gFAAI,WAAU;0FAAyB,KAAK,WAAW;;;;;;;;;;;;oEAGzD,KAAK,KAAK,kBACT,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wEACT,SAAS;4EAAE,OAAO;wEAAE;wEACpB,SAAS;4EAAE,OAAO;wEAAE;wEACpB,WAAW,CAAC,8CAA8C,EACxD,KAAK,KAAK,KAAK,MACX,4BACA,6BACJ;kFAED,KAAK,KAAK;;;;;;;;;;;;;;;;;wDAOpB,eAAe,KAAK,KAAK,kBACxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,SAAS;gEAAE,OAAO;4DAAE;4DACpB,SAAS;gEAAE,OAAO;4DAAE;4DACpB,WAAU;;;;;;;;;;;;;;;;;;uCAxEb,KAAK,IAAI;;;;;gCA+EpB;;;;;;;;;;;sCAIJ,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,SAAS;gCACT,WAAW,CAAC,mHAAmH,EAC7H,cAAc,mBAAmB,IACjC;gCACF,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,OAAO,cAAc,WAAW;;kDAEhC,6LAAC,iNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,6LAAC,4LAAA,CAAA,kBAAe;kDACb,CAAC,6BACA,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;4CACV,SAAS;gDAAE,SAAS;gDAAG,GAAG,CAAC;4CAAG;4CAC9B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,MAAM;gDAAE,SAAS;gDAAG,GAAG,CAAC;4CAAG;4CAC3B,YAAY;gDAAE,UAAU;4CAAI;4CAC5B,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,iNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;8CACxB,6LAAC;oCAAG,WAAU;8CAA+C;;;;;;;;;;;;sCAE/D,6LAAC;4BAAI,WAAU;;gCACZ,sBACC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDACb,gBAAgB,KAAK,KAAK;;;;;;;;;;;;;;;;8CAKnC,6LAAC;oCACC,MAAK;oCACL,WAAU;8CAEV,cAAA,6LAAC,yMAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAEtB,6LAAC;oCACC,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,eAAe;8CAE9B,cAAA,6LAAC,yMAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlC;GA1YwB;;QAUL,qIAAA,CAAA,cAAW;QACb,qIAAA,CAAA,YAAS;;;KAXF", "debugId": null}}, {"offset": {"line": 1865, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/layout/SidebarContext.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useState, useEffect, ReactNode } from 'react'\n\ninterface SidebarContextType {\n  isCollapsed: boolean\n  setIsCollapsed: (collapsed: boolean) => void\n  toggleCollapsed: () => void\n}\n\nconst SidebarContext = createContext<SidebarContextType | undefined>(undefined)\n\nexport function SidebarProvider({ children }: { children: ReactNode }) {\n  const [isCollapsed, setIsCollapsed] = useState(false)\n\n  useEffect(() => {\n    // Load sidebar state from localStorage\n    const savedCollapsed = localStorage.getItem('sidebar-collapsed')\n    if (savedCollapsed) {\n      setIsCollapsed(JSON.parse(savedCollapsed))\n    }\n  }, [])\n\n  const toggleCollapsed = () => {\n    const newState = !isCollapsed\n    setIsCollapsed(newState)\n    localStorage.setItem('sidebar-collapsed', JSON.stringify(newState))\n  }\n\n  return (\n    <SidebarContext.Provider value={{ isCollapsed, setIsCollapsed, toggleCollapsed }}>\n      {children}\n    </SidebarContext.Provider>\n  )\n}\n\nexport function useSidebar() {\n  const context = useContext(SidebarContext)\n  if (context === undefined) {\n    throw new Error('useSidebar must be used within a SidebarProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAUA,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAkC;AAE9D,SAAS,gBAAgB,EAAE,QAAQ,EAA2B;;IACnE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,uCAAuC;YACvC,MAAM,iBAAiB,aAAa,OAAO,CAAC;YAC5C,IAAI,gBAAgB;gBAClB,eAAe,KAAK,KAAK,CAAC;YAC5B;QACF;oCAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB,MAAM,WAAW,CAAC;QAClB,eAAe;QACf,aAAa,OAAO,CAAC,qBAAqB,KAAK,SAAS,CAAC;IAC3D;IAEA,qBACE,6LAAC,eAAe,QAAQ;QAAC,OAAO;YAAE;YAAa;YAAgB;QAAgB;kBAC5E;;;;;;AAGP;GAtBgB;KAAA;AAwBT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}, {"offset": {"line": 1928, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/layout/LayoutProvider.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { isMobile } from '@/lib/capacitor'\nimport MobileLayout from '@/components/mobile/MobileLayout'\nimport Sidebar from '@/components/layout/Sidebar'\nimport { SidebarProvider, useSidebar } from '@/components/layout/SidebarContext'\n\ninterface LayoutProviderProps {\n  children: React.ReactNode\n}\n\nfunction LayoutContent({ children }: LayoutProviderProps) {\n  const { isCollapsed } = useSidebar()\n  const [isMobileDevice, setIsMobileDevice] = useState(false)\n  const [mounted, setMounted] = useState(false)\n\n  useEffect(() => {\n    setIsMobileDevice(isMobile())\n    setMounted(true)\n  }, [])\n\n  // Prevent hydration mismatch by not rendering until mounted\n  if (!mounted) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    )\n  }\n\n  // Use mobile layout for mobile devices\n  if (isMobileDevice) {\n    return <MobileLayout>{children}</MobileLayout>\n  }\n\n  // Use desktop layout for larger screens\n  return (\n    <div className=\"min-h-screen bg-gray-100\">\n      <Sidebar />\n      {/* Main content area with proper left margin to account for fixed sidebar */}\n      <div\n        className={`transition-all duration-300 ${\n          isCollapsed ? 'md:ml-20' : 'md:ml-72'\n        }`}\n      >\n        <main className=\"min-h-screen bg-gray-50\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  )\n}\n\nexport default function LayoutProvider({ children }: LayoutProviderProps) {\n  const [isMobileDevice, setIsMobileDevice] = useState(false)\n  const [mounted, setMounted] = useState(false)\n\n  useEffect(() => {\n    setIsMobileDevice(isMobile())\n    setMounted(true)\n  }, [])\n\n  // Prevent hydration mismatch by not rendering until mounted\n  if (!mounted) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    )\n  }\n\n  // Use mobile layout for mobile devices\n  if (isMobileDevice) {\n    return <MobileLayout>{children}</MobileLayout>\n  }\n\n  // Use desktop layout with sidebar context\n  return (\n    <SidebarProvider>\n      <LayoutContent>{children}</LayoutContent>\n    </SidebarProvider>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAYA,SAAS,cAAc,EAAE,QAAQ,EAAuB;;IACtD,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACjC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,kBAAkB,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;YACzB,WAAW;QACb;kCAAG,EAAE;IAEL,4DAA4D;IAC5D,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,uCAAuC;IACvC,IAAI,gBAAgB;QAClB,qBAAO,6LAAC,+IAAA,CAAA,UAAY;sBAAE;;;;;;IACxB;IAEA,wCAAwC;IACxC,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,0IAAA,CAAA,UAAO;;;;;0BAER,6LAAC;gBACC,WAAW,CAAC,4BAA4B,EACtC,cAAc,aAAa,YAC3B;0BAEF,cAAA,6LAAC;oBAAK,WAAU;8BACd,cAAA,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;;;;;AAMb;GA1CS;;QACiB,iJAAA,CAAA,aAAU;;;KAD3B;AA4CM,SAAS,eAAe,EAAE,QAAQ,EAAuB;;IACtE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,kBAAkB,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;YACzB,WAAW;QACb;mCAAG,EAAE;IAEL,4DAA4D;IAC5D,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,uCAAuC;IACvC,IAAI,gBAAgB;QAClB,qBAAO,6LAAC,+IAAA,CAAA,UAAY;sBAAE;;;;;;IACxB;IAEA,0CAA0C;IAC1C,qBACE,6LAAC,iJAAA,CAAA,kBAAe;kBACd,cAAA,6LAAC;sBAAe;;;;;;;;;;;AAGtB;IA7BwB;MAAA", "debugId": null}}]}