{"version": 3, "file": "plugin.cjs.js", "sources": ["esm/index.js", "esm/web.js"], "sourcesContent": ["import { registerPlugin } from '@capacitor/core';\nconst Device = registerPlugin('Device', {\n    web: () => import('./web').then(m => new m.DeviceWeb()),\n});\nexport * from './definitions';\nexport { Device };\n//# sourceMappingURL=index.js.map", "import { WebPlugin } from '@capacitor/core';\nexport class <PERSON>ceWeb extends WebPlugin {\n    async getId() {\n        return {\n            identifier: this.getUid(),\n        };\n    }\n    async getInfo() {\n        if (typeof navigator === 'undefined' || !navigator.userAgent) {\n            throw this.unavailable('Device API not available in this browser');\n        }\n        const ua = navigator.userAgent;\n        const uaFields = this.parseUa(ua);\n        return {\n            model: uaFields.model,\n            platform: 'web',\n            operatingSystem: uaFields.operatingSystem,\n            osVersion: uaFields.osVersion,\n            manufacturer: navigator.vendor,\n            isVirtual: false,\n            webViewVersion: uaFields.browserVersion,\n        };\n    }\n    async getBatteryInfo() {\n        if (typeof navigator === 'undefined' || !navigator.getBattery) {\n            throw this.unavailable('Device API not available in this browser');\n        }\n        let battery = {};\n        try {\n            battery = await navigator.getBattery();\n        }\n        catch (e) {\n            // Let it fail, we don't care\n        }\n        return {\n            batteryLevel: battery.level,\n            isCharging: battery.charging,\n        };\n    }\n    async getLanguageCode() {\n        return {\n            value: navigator.language.split('-')[0].toLowerCase(),\n        };\n    }\n    async getLanguageTag() {\n        return {\n            value: navigator.language,\n        };\n    }\n    parseUa(ua) {\n        const uaFields = {};\n        const start = ua.indexOf('(') + 1;\n        let end = ua.indexOf(') AppleWebKit');\n        if (ua.indexOf(') Gecko') !== -1) {\n            end = ua.indexOf(') Gecko');\n        }\n        const fields = ua.substring(start, end);\n        if (ua.indexOf('Android') !== -1) {\n            const tmpFields = fields.replace('; wv', '').split('; ').pop();\n            if (tmpFields) {\n                uaFields.model = tmpFields.split(' Build')[0];\n            }\n            uaFields.osVersion = fields.split('; ')[1];\n        }\n        else {\n            uaFields.model = fields.split('; ')[0];\n            if (typeof navigator !== 'undefined' && navigator.oscpu) {\n                uaFields.osVersion = navigator.oscpu;\n            }\n            else {\n                if (ua.indexOf('Windows') !== -1) {\n                    uaFields.osVersion = fields;\n                }\n                else {\n                    const tmpFields = fields.split('; ').pop();\n                    if (tmpFields) {\n                        const lastParts = tmpFields\n                            .replace(' like Mac OS X', '')\n                            .split(' ');\n                        uaFields.osVersion = lastParts[lastParts.length - 1].replace(/_/g, '.');\n                    }\n                }\n            }\n        }\n        if (/android/i.test(ua)) {\n            uaFields.operatingSystem = 'android';\n        }\n        else if (/iPad|iPhone|iPod/.test(ua) && !window.MSStream) {\n            uaFields.operatingSystem = 'ios';\n        }\n        else if (/Win/.test(ua)) {\n            uaFields.operatingSystem = 'windows';\n        }\n        else if (/Mac/i.test(ua)) {\n            uaFields.operatingSystem = 'mac';\n        }\n        else {\n            uaFields.operatingSystem = 'unknown';\n        }\n        // Check for browsers based on non-standard javascript apis, only not user agent\n        const isSafari = !!window.ApplePaySession;\n        const isChrome = !!window.chrome;\n        const isFirefox = /Firefox/.test(ua);\n        const isEdge = /Edg/.test(ua);\n        const isFirefoxIOS = /FxiOS/.test(ua);\n        const isChromeIOS = /CriOS/.test(ua);\n        const isEdgeIOS = /EdgiOS/.test(ua);\n        // FF and Edge User Agents both end with \"/MAJOR.MINOR\"\n        if (isSafari ||\n            (isChrome && !isEdge) ||\n            isFirefoxIOS ||\n            isChromeIOS ||\n            isEdgeIOS) {\n            // Safari version comes as     \"... Version/MAJOR.MINOR ...\"\n            // Chrome version comes as     \"... Chrome/MAJOR.MINOR ...\"\n            // FirefoxIOS version comes as \"... FxiOS/MAJOR.MINOR ...\"\n            // ChromeIOS version comes as  \"... CriOS/MAJOR.MINOR ...\"\n            let searchWord;\n            if (isFirefoxIOS) {\n                searchWord = 'FxiOS';\n            }\n            else if (isChromeIOS) {\n                searchWord = 'CriOS';\n            }\n            else if (isEdgeIOS) {\n                searchWord = 'EdgiOS';\n            }\n            else if (isSafari) {\n                searchWord = 'Version';\n            }\n            else {\n                searchWord = 'Chrome';\n            }\n            const words = ua.split(' ');\n            for (const word of words) {\n                if (word.includes(searchWord)) {\n                    const version = word.split('/')[1];\n                    uaFields.browserVersion = version;\n                }\n            }\n        }\n        else if (isFirefox || isEdge) {\n            const reverseUA = ua.split('').reverse().join('');\n            const reverseVersion = reverseUA.split('/')[0];\n            const version = reverseVersion.split('').reverse().join('');\n            uaFields.browserVersion = version;\n        }\n        return uaFields;\n    }\n    getUid() {\n        if (typeof window !== 'undefined' && window.localStorage) {\n            let uid = window.localStorage.getItem('_capuid');\n            if (uid) {\n                return uid;\n            }\n            uid = this.uuid4();\n            window.localStorage.setItem('_capuid', uid);\n            return uid;\n        }\n        return this.uuid4();\n    }\n    uuid4() {\n        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n            const r = (Math.random() * 16) | 0, v = c === 'x' ? r : (r & 0x3) | 0x8;\n            return v.toString(16);\n        });\n    }\n}\n//# sourceMappingURL=web.js.map"], "names": ["registerPlugin", "WebPlugin"], "mappings": ";;;;AACK,MAAC,MAAM,GAAGA,mBAAc,CAAC,QAAQ,EAAE;AACxC,IAAI,GAAG,EAAE,MAAM,mDAAe,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,SAAS,EAAE,CAAC;AAC3D,CAAC;;ACFM,MAAM,SAAS,SAASC,cAAS,CAAC;AACzC,IAAI,MAAM,KAAK,GAAG;AAClB,QAAQ,OAAO;AACf,YAAY,UAAU,EAAE,IAAI,CAAC,MAAM,EAAE;AACrC,SAAS;AACT;AACA,IAAI,MAAM,OAAO,GAAG;AACpB,QAAQ,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;AACtE,YAAY,MAAM,IAAI,CAAC,WAAW,CAAC,0CAA0C,CAAC;AAC9E;AACA,QAAQ,MAAM,EAAE,GAAG,SAAS,CAAC,SAAS;AACtC,QAAQ,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;AACzC,QAAQ,OAAO;AACf,YAAY,KAAK,EAAE,QAAQ,CAAC,KAAK;AACjC,YAAY,QAAQ,EAAE,KAAK;AAC3B,YAAY,eAAe,EAAE,QAAQ,CAAC,eAAe;AACrD,YAAY,SAAS,EAAE,QAAQ,CAAC,SAAS;AACzC,YAAY,YAAY,EAAE,SAAS,CAAC,MAAM;AAC1C,YAAY,SAAS,EAAE,KAAK;AAC5B,YAAY,cAAc,EAAE,QAAQ,CAAC,cAAc;AACnD,SAAS;AACT;AACA,IAAI,MAAM,cAAc,GAAG;AAC3B,QAAQ,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE;AACvE,YAAY,MAAM,IAAI,CAAC,WAAW,CAAC,0CAA0C,CAAC;AAC9E;AACA,QAAQ,IAAI,OAAO,GAAG,EAAE;AACxB,QAAQ,IAAI;AACZ,YAAY,OAAO,GAAG,MAAM,SAAS,CAAC,UAAU,EAAE;AAClD;AACA,QAAQ,OAAO,CAAC,EAAE;AAClB;AACA;AACA,QAAQ,OAAO;AACf,YAAY,YAAY,EAAE,OAAO,CAAC,KAAK;AACvC,YAAY,UAAU,EAAE,OAAO,CAAC,QAAQ;AACxC,SAAS;AACT;AACA,IAAI,MAAM,eAAe,GAAG;AAC5B,QAAQ,OAAO;AACf,YAAY,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE;AACjE,SAAS;AACT;AACA,IAAI,MAAM,cAAc,GAAG;AAC3B,QAAQ,OAAO;AACf,YAAY,KAAK,EAAE,SAAS,CAAC,QAAQ;AACrC,SAAS;AACT;AACA,IAAI,OAAO,CAAC,EAAE,EAAE;AAChB,QAAQ,MAAM,QAAQ,GAAG,EAAE;AAC3B,QAAQ,MAAM,KAAK,GAAG,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC;AACzC,QAAQ,IAAI,GAAG,GAAG,EAAE,CAAC,OAAO,CAAC,eAAe,CAAC;AAC7C,QAAQ,IAAI,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,EAAE,EAAE;AAC1C,YAAY,GAAG,GAAG,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC;AACvC;AACA,QAAQ,MAAM,MAAM,GAAG,EAAE,CAAC,SAAS,CAAC,KAAK,EAAE,GAAG,CAAC;AAC/C,QAAQ,IAAI,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,EAAE,EAAE;AAC1C,YAAY,MAAM,SAAS,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE;AAC1E,YAAY,IAAI,SAAS,EAAE;AAC3B,gBAAgB,QAAQ,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC7D;AACA,YAAY,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACtD;AACA,aAAa;AACb,YAAY,QAAQ,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClD,YAAY,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,KAAK,EAAE;AACrE,gBAAgB,QAAQ,CAAC,SAAS,GAAG,SAAS,CAAC,KAAK;AACpD;AACA,iBAAiB;AACjB,gBAAgB,IAAI,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,EAAE,EAAE;AAClD,oBAAoB,QAAQ,CAAC,SAAS,GAAG,MAAM;AAC/C;AACA,qBAAqB;AACrB,oBAAoB,MAAM,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE;AAC9D,oBAAoB,IAAI,SAAS,EAAE;AACnC,wBAAwB,MAAM,SAAS,GAAG;AAC1C,6BAA6B,OAAO,CAAC,gBAAgB,EAAE,EAAE;AACzD,6BAA6B,KAAK,CAAC,GAAG,CAAC;AACvC,wBAAwB,QAAQ,CAAC,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;AAC/F;AACA;AACA;AACA;AACA,QAAQ,IAAI,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;AACjC,YAAY,QAAQ,CAAC,eAAe,GAAG,SAAS;AAChD;AACA,aAAa,IAAI,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;AAClE,YAAY,QAAQ,CAAC,eAAe,GAAG,KAAK;AAC5C;AACA,aAAa,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;AACjC,YAAY,QAAQ,CAAC,eAAe,GAAG,SAAS;AAChD;AACA,aAAa,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;AAClC,YAAY,QAAQ,CAAC,eAAe,GAAG,KAAK;AAC5C;AACA,aAAa;AACb,YAAY,QAAQ,CAAC,eAAe,GAAG,SAAS;AAChD;AACA;AACA,QAAQ,MAAM,QAAQ,GAAG,CAAC,CAAC,MAAM,CAAC,eAAe;AACjD,QAAQ,MAAM,QAAQ,GAAG,CAAC,CAAC,MAAM,CAAC,MAAM;AACxC,QAAQ,MAAM,SAAS,GAAG,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;AAC5C,QAAQ,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;AACrC,QAAQ,MAAM,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;AAC7C,QAAQ,MAAM,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;AAC5C,QAAQ,MAAM,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;AAC3C;AACA,QAAQ,IAAI,QAAQ;AACpB,aAAa,QAAQ,IAAI,CAAC,MAAM,CAAC;AACjC,YAAY,YAAY;AACxB,YAAY,WAAW;AACvB,YAAY,SAAS,EAAE;AACvB;AACA;AACA;AACA;AACA,YAAY,IAAI,UAAU;AAC1B,YAAY,IAAI,YAAY,EAAE;AAC9B,gBAAgB,UAAU,GAAG,OAAO;AACpC;AACA,iBAAiB,IAAI,WAAW,EAAE;AAClC,gBAAgB,UAAU,GAAG,OAAO;AACpC;AACA,iBAAiB,IAAI,SAAS,EAAE;AAChC,gBAAgB,UAAU,GAAG,QAAQ;AACrC;AACA,iBAAiB,IAAI,QAAQ,EAAE;AAC/B,gBAAgB,UAAU,GAAG,SAAS;AACtC;AACA,iBAAiB;AACjB,gBAAgB,UAAU,GAAG,QAAQ;AACrC;AACA,YAAY,MAAM,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC;AACvC,YAAY,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;AACtC,gBAAgB,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE;AAC/C,oBAAoB,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACtD,oBAAoB,QAAQ,CAAC,cAAc,GAAG,OAAO;AACrD;AACA;AACA;AACA,aAAa,IAAI,SAAS,IAAI,MAAM,EAAE;AACtC,YAAY,MAAM,SAAS,GAAG,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;AAC7D,YAAY,MAAM,cAAc,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC1D,YAAY,MAAM,OAAO,GAAG,cAAc,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;AACvE,YAAY,QAAQ,CAAC,cAAc,GAAG,OAAO;AAC7C;AACA,QAAQ,OAAO,QAAQ;AACvB;AACA,IAAI,MAAM,GAAG;AACb,QAAQ,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,YAAY,EAAE;AAClE,YAAY,IAAI,GAAG,GAAG,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC;AAC5D,YAAY,IAAI,GAAG,EAAE;AACrB,gBAAgB,OAAO,GAAG;AAC1B;AACA,YAAY,GAAG,GAAG,IAAI,CAAC,KAAK,EAAE;AAC9B,YAAY,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;AACvD,YAAY,OAAO,GAAG;AACtB;AACA,QAAQ,OAAO,IAAI,CAAC,KAAK,EAAE;AAC3B;AACA,IAAI,KAAK,GAAG;AACZ,QAAQ,OAAO,sCAAsC,CAAC,OAAO,CAAC,OAAO,EAAE,UAAU,CAAC,EAAE;AACpF,YAAY,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG;AACnF,YAAY,OAAO,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC;AACjC,SAAS,CAAC;AACV;AACA;;;;;;;;;"}