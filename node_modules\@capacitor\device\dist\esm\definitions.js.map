{"version": 3, "file": "definitions.js", "sourceRoot": "", "sources": ["../../src/definitions.ts"], "names": [], "mappings": "", "sourcesContent": ["export type OperatingSystem = 'ios' | 'android' | 'windows' | 'mac' | 'unknown';\n\nexport interface DeviceId {\n  /**\n   * The identifier of the device as available to the app. This identifier may change\n   * on modern mobile platforms that only allow per-app install ids.\n   *\n   * On iOS, the identifier is a UUID that uniquely identifies a device to the app’s vendor ([read more](https://developer.apple.com/documentation/uikit/uidevice/1620059-identifierforvendor)).\n   *\n   * on Android 8+, __the identifier is a 64-bit number (expressed as a hexadecimal string)__, unique to each combination of app-signing key, user, and device ([read more](https://developer.android.com/reference/android/provider/Settings.Secure#ANDROID_ID)).\n   *\n   * On web, a random identifier is generated and stored on localStorage for subsequent calls.\n   * If localStorage is not available a new random identifier will be generated on every call.\n   *\n   * @since 1.0.0\n   */\n  identifier: string;\n}\n\nexport interface DeviceInfo {\n  /**\n   * The name of the device. For example, \"<PERSON>'s iPhone\".\n   *\n   * This is only supported on iOS and Android 7.1 or above.\n   *\n   * On iOS 16+ this will return a generic device name without the appropriate [entitlements](https://developer.apple.com/documentation/bundleresources/entitlements/com_apple_developer_device-information_user-assigned-device-name).\n   *\n   * @since 1.0.0\n   */\n  name?: string;\n\n  /**\n   * The device model. For example, \"iPhone13,4\".\n   *\n   * @since 1.0.0\n   */\n  model: string;\n\n  /**\n   * The device platform (lowercase).\n   *\n   * @since 1.0.0\n   */\n  platform: 'ios' | 'android' | 'web';\n\n  /**\n   * The operating system of the device.\n   *\n   * @since 1.0.0\n   */\n  operatingSystem: OperatingSystem;\n\n  /**\n   * The version of the device OS.\n   *\n   * @since 1.0.0\n   */\n  osVersion: string;\n\n  /**\n   * The iOS version number.\n   *\n   * Only available on iOS.\n   *\n   * Multi-part version numbers are crushed down into an integer padded to two-digits, ex: `\"16.3.1\"` -> `160301`\n   *\n   * @since 5.0.0\n   */\n  iOSVersion?: number;\n\n  /**\n   * The Android SDK version number.\n   *\n   * Only available on Android.\n   *\n   * @since 5.0.0\n   */\n  androidSDKVersion?: number;\n\n  /**\n   * The manufacturer of the device.\n   *\n   * @since 1.0.0\n   */\n  manufacturer: string;\n\n  /**\n   * Whether the app is running in a simulator/emulator.\n   *\n   * @since 1.0.0\n   */\n  isVirtual: boolean;\n\n  /**\n   * Approximate memory used by the current app, in bytes. Divide by\n   * 1048576 to get the number of MBs used.\n   *\n   * @since 1.0.0\n   */\n  memUsed?: number;\n\n  /**\n   * The web view browser version\n   *\n   * @since 1.0.0\n   */\n  webViewVersion: string;\n}\n\nexport interface BatteryInfo {\n  /**\n   * A percentage (0 to 1) indicating how much the battery is charged.\n   *\n   * @since 1.0.0\n   */\n  batteryLevel?: number;\n\n  /**\n   * Whether the device is charging.\n   *\n   * @since 1.0.0\n   */\n  isCharging?: boolean;\n}\n\nexport interface GetLanguageCodeResult {\n  /**\n   * Two character language code.\n   *\n   * @since 1.0.0\n   */\n  value: string;\n}\n\nexport interface LanguageTag {\n  /**\n   * Returns a well-formed IETF BCP 47 language tag.\n   *\n   * @since 4.0.0\n   */\n  value: string;\n}\n\nexport interface DevicePlugin {\n  /**\n   * Return an unique identifier for the device.\n   *\n   * @since 1.0.0\n   */\n  getId(): Promise<DeviceId>;\n\n  /**\n   * Return information about the underlying device/os/platform.\n   *\n   * @since 1.0.0\n   */\n  getInfo(): Promise<DeviceInfo>;\n\n  /**\n   * Return information about the battery.\n   *\n   * @since 1.0.0\n   */\n  getBatteryInfo(): Promise<BatteryInfo>;\n\n  /**\n   * Get the device's current language locale code.\n   *\n   * @since 1.0.0\n   */\n  getLanguageCode(): Promise<GetLanguageCodeResult>;\n\n  /**\n   * Get the device's current language locale tag.\n   *\n   * @since 4.0.0\n   */\n  getLanguageTag(): Promise<LanguageTag>;\n}\n"]}