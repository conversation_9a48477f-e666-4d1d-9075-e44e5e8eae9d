{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "web.js", "sourceRoot": "", "sources": ["file:///C:/QuantAxiom/Products/LifeManager/node_modules/%40capacitor/toast/src/web.ts"], "sourcesContent": ["import { WebPlugin } from '@capacitor/core';\n\nimport type { ToastPlugin, ShowOptions } from './definitions';\n\nexport class ToastWeb extends WebPlugin implements ToastPlugin {\n  async show(options: ShowOptions): Promise<void> {\n    if (typeof document !== 'undefined') {\n      let duration = 2000;\n      if (options.duration) {\n        duration = options.duration === 'long' ? 3500 : 2000;\n      }\n      const toast = document.createElement('pwa-toast') as any;\n      toast.duration = duration;\n      toast.message = options.text;\n      document.body.appendChild(toast);\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;;AAItC,MAAO,QAAS,iKAAQ,YAAS;IACrC,KAAK,CAAC,IAAI,CAAC,OAAoB,EAAA;QAC7B,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;YACnC,IAAI,QAAQ,GAAG,IAAI,CAAC;YACpB,IAAI,OAAO,CAAC,QAAQ,EAAE;gBACpB,QAAQ,GAAG,OAAO,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;aACtD;YACD,MAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,WAAW,CAAQ,CAAC;YACzD,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;YAC1B,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;YAC7B,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;SAClC;IACH,CAAC;CACF", "ignoreList": [0], "debugId": null}}]}