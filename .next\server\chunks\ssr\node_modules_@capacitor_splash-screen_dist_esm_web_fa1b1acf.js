module.exports = {

"[project]/node_modules/@capacitor/splash-screen/dist/esm/web.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SplashScreenWeb": (()=>SplashScreenWeb)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$capacitor$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@capacitor/core/dist/index.js [app-ssr] (ecmascript)");
;
class SplashScreenWeb extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$capacitor$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WebPlugin"] {
    async show(_options) {
        return undefined;
    }
    async hide(_options) {
        return undefined;
    }
} //# sourceMappingURL=web.js.map
}}),

};

//# sourceMappingURL=node_modules_%40capacitor_splash-screen_dist_esm_web_fa1b1acf.js.map