{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "web.js", "sourceRoot": "", "sources": ["file:///C:/QuantAxiom/Products/LifeManager/node_modules/%40capacitor/share/src/web.ts"], "sourcesContent": ["import { WebPlugin } from '@capacitor/core';\n\nimport type {\n  CanShareResult,\n  ShareOptions,\n  SharePlugin,\n  ShareResult,\n} from './definitions';\n\nexport class ShareWeb extends WebPlugin implements SharePlugin {\n  async canShare(): Promise<CanShareResult> {\n    if (typeof navigator === 'undefined' || !navigator.share) {\n      return { value: false };\n    } else {\n      return { value: true };\n    }\n  }\n  async share(options: ShareOptions): Promise<ShareResult> {\n    if (typeof navigator === 'undefined' || !navigator.share) {\n      throw this.unavailable('Share API not available in this browser');\n    }\n\n    await navigator.share({\n      title: options.title,\n      text: options.text,\n      url: options.url,\n    });\n    return {};\n  }\n}\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;;AAStC,MAAO,QAAS,8JAAQ,YAAS;IACrC,KAAK,CAAC,QAAQ,GAAA;QACZ,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;YACxD,OAAO;gBAAE,KAAK,EAAE,KAAK;YAAA,CAAE,CAAC;SACzB,MAAM;YACL,OAAO;gBAAE,KAAK,EAAE,IAAI;YAAA,CAAE,CAAC;SACxB;IACH,CAAC;IACD,KAAK,CAAC,KAAK,CAAC,OAAqB,EAAA;QAC/B,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;YACxD,MAAM,IAAI,CAAC,WAAW,CAAC,yCAAyC,CAAC,CAAC;SACnE;QAED,MAAM,SAAS,CAAC,KAAK,CAAC;YACpB,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,GAAG,EAAE,OAAO,CAAC,GAAG;SACjB,CAAC,CAAC;QACH,OAAO,CAAA,CAAE,CAAC;IACZ,CAAC;CACF", "ignoreList": [0], "debugId": null}}]}