{"version": 3, "file": "plugin.js", "sources": ["esm/index.js", "esm/web.js"], "sourcesContent": ["import { registerPlugin } from '@capacitor/core';\nconst Toast = registerPlugin('Toast', {\n    web: () => import('./web').then(m => new m.ToastWeb()),\n});\nexport * from './definitions';\nexport { Toast };\n//# sourceMappingURL=index.js.map", "import { WebPlugin } from '@capacitor/core';\nexport class ToastWeb extends WebPlugin {\n    async show(options) {\n        if (typeof document !== 'undefined') {\n            let duration = 2000;\n            if (options.duration) {\n                duration = options.duration === 'long' ? 3500 : 2000;\n            }\n            const toast = document.createElement('pwa-toast');\n            toast.duration = duration;\n            toast.message = options.text;\n            document.body.appendChild(toast);\n        }\n    }\n}\n//# sourceMappingURL=web.js.map"], "names": ["registerPlugin", "WebPlugin"], "mappings": ";;;AACK,UAAC,KAAK,GAAGA,mBAAc,CAAC,OAAO,EAAE;IACtC,IAAI,GAAG,EAAE,MAAM,mDAAe,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC;IAC1D,CAAC;;ICFM,MAAM,QAAQ,SAASC,cAAS,CAAC;IACxC,IAAI,MAAM,IAAI,CAAC,OAAO,EAAE;IACxB,QAAQ,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;IAC7C,YAAY,IAAI,QAAQ,GAAG,IAAI;IAC/B,YAAY,IAAI,OAAO,CAAC,QAAQ,EAAE;IAClC,gBAAgB,QAAQ,GAAG,OAAO,CAAC,QAAQ,KAAK,MAAM,GAAG,IAAI,GAAG,IAAI;IACpE;IACA,YAAY,MAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,WAAW,CAAC;IAC7D,YAAY,KAAK,CAAC,QAAQ,GAAG,QAAQ;IACrC,YAAY,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC,IAAI;IACxC,YAAY,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;IAC5C;IACA;IACA;;;;;;;;;;;;;;;"}