{"version": 3, "file": "plugin.cjs.js", "sources": ["esm/definitions.js", "esm/index.js", "esm/web.js"], "sourcesContent": ["export var ImpactStyle;\n(function (ImpactStyle) {\n    /**\n     * A collision between large, heavy user interface elements\n     *\n     * @since 1.0.0\n     */\n    ImpactStyle[\"Heavy\"] = \"HEAVY\";\n    /**\n     * A collision between moderately sized user interface elements\n     *\n     * @since 1.0.0\n     */\n    ImpactStyle[\"Medium\"] = \"MEDIUM\";\n    /**\n     * A collision between small, light user interface elements\n     *\n     * @since 1.0.0\n     */\n    ImpactStyle[\"Light\"] = \"LIGHT\";\n})(ImpactStyle || (ImpactStyle = {}));\nexport var NotificationType;\n(function (NotificationType) {\n    /**\n     * A notification feedback type indicating that a task has completed successfully\n     *\n     * @since 1.0.0\n     */\n    NotificationType[\"Success\"] = \"SUCCESS\";\n    /**\n     * A notification feedback type indicating that a task has produced a warning\n     *\n     * @since 1.0.0\n     */\n    NotificationType[\"Warning\"] = \"WARNING\";\n    /**\n     * A notification feedback type indicating that a task has failed\n     *\n     * @since 1.0.0\n     */\n    NotificationType[\"Error\"] = \"ERROR\";\n})(NotificationType || (NotificationType = {}));\n//# sourceMappingURL=definitions.js.map", "import { registerPlugin } from '@capacitor/core';\nconst Haptics = registerPlugin('Haptics', {\n    web: () => import('./web').then(m => new m.HapticsWeb()),\n});\nexport * from './definitions';\nexport { Haptics };\n//# sourceMappingURL=index.js.map", "import { WebPlugin } from '@capacitor/core';\nimport { ImpactStyle, NotificationType } from './definitions';\nexport class HapticsWeb extends WebPlugin {\n    constructor() {\n        super(...arguments);\n        this.selectionStarted = false;\n    }\n    async impact(options) {\n        const pattern = this.patternForImpact(options === null || options === void 0 ? void 0 : options.style);\n        this.vibrateWithPattern(pattern);\n    }\n    async notification(options) {\n        const pattern = this.patternForNotification(options === null || options === void 0 ? void 0 : options.type);\n        this.vibrateWithPattern(pattern);\n    }\n    async vibrate(options) {\n        const duration = (options === null || options === void 0 ? void 0 : options.duration) || 300;\n        this.vibrateWithPattern([duration]);\n    }\n    async selectionStart() {\n        this.selectionStarted = true;\n    }\n    async selectionChanged() {\n        if (this.selectionStarted) {\n            this.vibrateWithPattern([70]);\n        }\n    }\n    async selectionEnd() {\n        this.selectionStarted = false;\n    }\n    patternForImpact(style = ImpactStyle.Heavy) {\n        if (style === ImpactStyle.Medium) {\n            return [43];\n        }\n        else if (style === ImpactStyle.Light) {\n            return [20];\n        }\n        return [61];\n    }\n    patternForNotification(type = NotificationType.Success) {\n        if (type === NotificationType.Warning) {\n            return [30, 40, 30, 50, 60];\n        }\n        else if (type === NotificationType.Error) {\n            return [27, 45, 50];\n        }\n        return [35, 65, 21];\n    }\n    vibrateWithPattern(pattern) {\n        if (navigator.vibrate) {\n            navigator.vibrate(pattern);\n        }\n        else {\n            throw this.unavailable('Browser does not support the vibrate API');\n        }\n    }\n}\n//# sourceMappingURL=web.js.map"], "names": ["ImpactStyle", "NotificationType", "registerPlugin", "WebPlugin"], "mappings": ";;;;AAAWA;AACX,CAAC,UAAU,WAAW,EAAE;AACxB;AACA;AACA;AACA;AACA;AACA,IAAI,WAAW,CAAC,OAAO,CAAC,GAAG,OAAO;AAClC;AACA;AACA;AACA;AACA;AACA,IAAI,WAAW,CAAC,QAAQ,CAAC,GAAG,QAAQ;AACpC;AACA;AACA;AACA;AACA;AACA,IAAI,WAAW,CAAC,OAAO,CAAC,GAAG,OAAO;AAClC,CAAC,EAAEA,mBAAW,KAAKA,mBAAW,GAAG,EAAE,CAAC,CAAC;AAC1BC;AACX,CAAC,UAAU,gBAAgB,EAAE;AAC7B;AACA;AACA;AACA;AACA;AACA,IAAI,gBAAgB,CAAC,SAAS,CAAC,GAAG,SAAS;AAC3C;AACA;AACA;AACA;AACA;AACA,IAAI,gBAAgB,CAAC,SAAS,CAAC,GAAG,SAAS;AAC3C;AACA;AACA;AACA;AACA;AACA,IAAI,gBAAgB,CAAC,OAAO,CAAC,GAAG,OAAO;AACvC,CAAC,EAAEA,wBAAgB,KAAKA,wBAAgB,GAAG,EAAE,CAAC,CAAC;;ACxC1C,MAAC,OAAO,GAAGC,mBAAc,CAAC,SAAS,EAAE;AAC1C,IAAI,GAAG,EAAE,MAAM,mDAAe,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,UAAU,EAAE,CAAC;AAC5D,CAAC;;ACDM,MAAM,UAAU,SAASC,cAAS,CAAC;AAC1C,IAAI,WAAW,GAAG;AAClB,QAAQ,KAAK,CAAC,GAAG,SAAS,CAAC;AAC3B,QAAQ,IAAI,CAAC,gBAAgB,GAAG,KAAK;AACrC;AACA,IAAI,MAAM,MAAM,CAAC,OAAO,EAAE;AAC1B,QAAQ,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,MAAM,GAAG,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC;AAC9G,QAAQ,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;AACxC;AACA,IAAI,MAAM,YAAY,CAAC,OAAO,EAAE;AAChC,QAAQ,MAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,MAAM,GAAG,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;AACnH,QAAQ,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;AACxC;AACA,IAAI,MAAM,OAAO,CAAC,OAAO,EAAE;AAC3B,QAAQ,MAAM,QAAQ,GAAG,CAAC,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,MAAM,GAAG,MAAM,GAAG,OAAO,CAAC,QAAQ,KAAK,GAAG;AACpG,QAAQ,IAAI,CAAC,kBAAkB,CAAC,CAAC,QAAQ,CAAC,CAAC;AAC3C;AACA,IAAI,MAAM,cAAc,GAAG;AAC3B,QAAQ,IAAI,CAAC,gBAAgB,GAAG,IAAI;AACpC;AACA,IAAI,MAAM,gBAAgB,GAAG;AAC7B,QAAQ,IAAI,IAAI,CAAC,gBAAgB,EAAE;AACnC,YAAY,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,CAAC,CAAC;AACzC;AACA;AACA,IAAI,MAAM,YAAY,GAAG;AACzB,QAAQ,IAAI,CAAC,gBAAgB,GAAG,KAAK;AACrC;AACA,IAAI,gBAAgB,CAAC,KAAK,GAAGH,mBAAW,CAAC,KAAK,EAAE;AAChD,QAAQ,IAAI,KAAK,KAAKA,mBAAW,CAAC,MAAM,EAAE;AAC1C,YAAY,OAAO,CAAC,EAAE,CAAC;AACvB;AACA,aAAa,IAAI,KAAK,KAAKA,mBAAW,CAAC,KAAK,EAAE;AAC9C,YAAY,OAAO,CAAC,EAAE,CAAC;AACvB;AACA,QAAQ,OAAO,CAAC,EAAE,CAAC;AACnB;AACA,IAAI,sBAAsB,CAAC,IAAI,GAAGC,wBAAgB,CAAC,OAAO,EAAE;AAC5D,QAAQ,IAAI,IAAI,KAAKA,wBAAgB,CAAC,OAAO,EAAE;AAC/C,YAAY,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACvC;AACA,aAAa,IAAI,IAAI,KAAKA,wBAAgB,CAAC,KAAK,EAAE;AAClD,YAAY,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AAC/B;AACA,QAAQ,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AAC3B;AACA,IAAI,kBAAkB,CAAC,OAAO,EAAE;AAChC,QAAQ,IAAI,SAAS,CAAC,OAAO,EAAE;AAC/B,YAAY,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC;AACtC;AACA,aAAa;AACb,YAAY,MAAM,IAAI,CAAC,WAAW,CAAC,0CAA0C,CAAC;AAC9E;AACA;AACA;;;;;;;;;"}