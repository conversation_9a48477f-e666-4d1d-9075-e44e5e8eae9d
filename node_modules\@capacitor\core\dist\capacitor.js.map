{"version": 3, "file": "capacitor.js", "sources": ["../build/util.js", "../build/runtime.js", "../build/global.js", "../build/web-plugin.js", "../build/core-plugins.js"], "sourcesContent": ["export var ExceptionCode;\n(function (ExceptionCode) {\n    /**\n     * API is not implemented.\n     *\n     * This usually means the API can't be used because it is not implemented for\n     * the current platform.\n     */\n    ExceptionCode[\"Unimplemented\"] = \"UNIMPLEMENTED\";\n    /**\n     * API is not available.\n     *\n     * This means the API can't be used right now because:\n     *   - it is currently missing a prerequisite, such as network connectivity\n     *   - it requires a particular platform or browser version\n     */\n    ExceptionCode[\"Unavailable\"] = \"UNAVAILABLE\";\n})(ExceptionCode || (ExceptionCode = {}));\nexport class CapacitorException extends Error {\n    constructor(message, code, data) {\n        super(message);\n        this.message = message;\n        this.code = code;\n        this.data = data;\n    }\n}\nexport const getPlatformId = (win) => {\n    var _a, _b;\n    if (win === null || win === void 0 ? void 0 : win.androidBridge) {\n        return 'android';\n    }\n    else if ((_b = (_a = win === null || win === void 0 ? void 0 : win.webkit) === null || _a === void 0 ? void 0 : _a.messageHandlers) === null || _b === void 0 ? void 0 : _b.bridge) {\n        return 'ios';\n    }\n    else {\n        return 'web';\n    }\n};\n//# sourceMappingURL=util.js.map", "import { CapacitorException, getPlatformId, ExceptionCode } from './util';\nexport const createCapacitor = (win) => {\n    const capCustomPlatform = win.CapacitorCustomPlatform || null;\n    const cap = win.Capacitor || {};\n    const Plugins = (cap.Plugins = cap.Plugins || {});\n    const getPlatform = () => {\n        return capCustomPlatform !== null ? capCustomPlatform.name : getPlatformId(win);\n    };\n    const isNativePlatform = () => getPlatform() !== 'web';\n    const isPluginAvailable = (pluginName) => {\n        const plugin = registeredPlugins.get(pluginName);\n        if (plugin === null || plugin === void 0 ? void 0 : plugin.platforms.has(getPlatform())) {\n            // JS implementation available for the current platform.\n            return true;\n        }\n        if (getPluginHeader(pluginName)) {\n            // Native implementation available.\n            return true;\n        }\n        return false;\n    };\n    const getPluginHeader = (pluginName) => { var _a; return (_a = cap.PluginHeaders) === null || _a === void 0 ? void 0 : _a.find((h) => h.name === pluginName); };\n    const handleError = (err) => win.console.error(err);\n    const registeredPlugins = new Map();\n    const registerPlugin = (pluginName, jsImplementations = {}) => {\n        const registeredPlugin = registeredPlugins.get(pluginName);\n        if (registeredPlugin) {\n            console.warn(`Capacitor plugin \"${pluginName}\" already registered. Cannot register plugins twice.`);\n            return registeredPlugin.proxy;\n        }\n        const platform = getPlatform();\n        const pluginHeader = getPluginHeader(pluginName);\n        let jsImplementation;\n        const loadPluginImplementation = async () => {\n            if (!jsImplementation && platform in jsImplementations) {\n                jsImplementation =\n                    typeof jsImplementations[platform] === 'function'\n                        ? (jsImplementation = await jsImplementations[platform]())\n                        : (jsImplementation = jsImplementations[platform]);\n            }\n            else if (capCustomPlatform !== null && !jsImplementation && 'web' in jsImplementations) {\n                jsImplementation =\n                    typeof jsImplementations['web'] === 'function'\n                        ? (jsImplementation = await jsImplementations['web']())\n                        : (jsImplementation = jsImplementations['web']);\n            }\n            return jsImplementation;\n        };\n        const createPluginMethod = (impl, prop) => {\n            var _a, _b;\n            if (pluginHeader) {\n                const methodHeader = pluginHeader === null || pluginHeader === void 0 ? void 0 : pluginHeader.methods.find((m) => prop === m.name);\n                if (methodHeader) {\n                    if (methodHeader.rtype === 'promise') {\n                        return (options) => cap.nativePromise(pluginName, prop.toString(), options);\n                    }\n                    else {\n                        return (options, callback) => cap.nativeCallback(pluginName, prop.toString(), options, callback);\n                    }\n                }\n                else if (impl) {\n                    return (_a = impl[prop]) === null || _a === void 0 ? void 0 : _a.bind(impl);\n                }\n            }\n            else if (impl) {\n                return (_b = impl[prop]) === null || _b === void 0 ? void 0 : _b.bind(impl);\n            }\n            else {\n                throw new CapacitorException(`\"${pluginName}\" plugin is not implemented on ${platform}`, ExceptionCode.Unimplemented);\n            }\n        };\n        const createPluginMethodWrapper = (prop) => {\n            let remove;\n            const wrapper = (...args) => {\n                const p = loadPluginImplementation().then((impl) => {\n                    const fn = createPluginMethod(impl, prop);\n                    if (fn) {\n                        const p = fn(...args);\n                        remove = p === null || p === void 0 ? void 0 : p.remove;\n                        return p;\n                    }\n                    else {\n                        throw new CapacitorException(`\"${pluginName}.${prop}()\" is not implemented on ${platform}`, ExceptionCode.Unimplemented);\n                    }\n                });\n                if (prop === 'addListener') {\n                    p.remove = async () => remove();\n                }\n                return p;\n            };\n            // Some flair ✨\n            wrapper.toString = () => `${prop.toString()}() { [capacitor code] }`;\n            Object.defineProperty(wrapper, 'name', {\n                value: prop,\n                writable: false,\n                configurable: false,\n            });\n            return wrapper;\n        };\n        const addListener = createPluginMethodWrapper('addListener');\n        const removeListener = createPluginMethodWrapper('removeListener');\n        const addListenerNative = (eventName, callback) => {\n            const call = addListener({ eventName }, callback);\n            const remove = async () => {\n                const callbackId = await call;\n                removeListener({\n                    eventName,\n                    callbackId,\n                }, callback);\n            };\n            const p = new Promise((resolve) => call.then(() => resolve({ remove })));\n            p.remove = async () => {\n                console.warn(`Using addListener() without 'await' is deprecated.`);\n                await remove();\n            };\n            return p;\n        };\n        const proxy = new Proxy({}, {\n            get(_, prop) {\n                switch (prop) {\n                    // https://github.com/facebook/react/issues/20030\n                    case '$$typeof':\n                        return undefined;\n                    case 'toJSON':\n                        return () => ({});\n                    case 'addListener':\n                        return pluginHeader ? addListenerNative : addListener;\n                    case 'removeListener':\n                        return removeListener;\n                    default:\n                        return createPluginMethodWrapper(prop);\n                }\n            },\n        });\n        Plugins[pluginName] = proxy;\n        registeredPlugins.set(pluginName, {\n            name: pluginName,\n            proxy,\n            platforms: new Set([...Object.keys(jsImplementations), ...(pluginHeader ? [platform] : [])]),\n        });\n        return proxy;\n    };\n    // Add in convertFileSrc for web, it will already be available in native context\n    if (!cap.convertFileSrc) {\n        cap.convertFileSrc = (filePath) => filePath;\n    }\n    cap.getPlatform = getPlatform;\n    cap.handleError = handleError;\n    cap.isNativePlatform = isNativePlatform;\n    cap.isPluginAvailable = isPluginAvailable;\n    cap.registerPlugin = registerPlugin;\n    cap.Exception = CapacitorException;\n    cap.DEBUG = !!cap.DEBUG;\n    cap.isLoggingEnabled = !!cap.isLoggingEnabled;\n    return cap;\n};\nexport const initCapacitorGlobal = (win) => (win.Capacitor = createCapacitor(win));\n//# sourceMappingURL=runtime.js.map", "import { initCapacitorGlobal } from './runtime';\nexport const Capacitor = /*#__PURE__*/ initCapacitorGlobal(typeof globalThis !== 'undefined'\n    ? globalThis\n    : typeof self !== 'undefined'\n        ? self\n        : typeof window !== 'undefined'\n            ? window\n            : typeof global !== 'undefined'\n                ? global\n                : {});\nexport const registerPlugin = Capacitor.registerPlugin;\n//# sourceMappingURL=global.js.map", "import { Capacitor } from './global';\nimport { ExceptionCode } from './util';\n/**\n * Base class web plugins should extend.\n */\nexport class WebPlugin {\n    constructor() {\n        this.listeners = {};\n        this.retainedEventArguments = {};\n        this.windowListeners = {};\n    }\n    addListener(eventName, listenerFunc) {\n        let firstListener = false;\n        const listeners = this.listeners[eventName];\n        if (!listeners) {\n            this.listeners[eventName] = [];\n            firstListener = true;\n        }\n        this.listeners[eventName].push(listenerFunc);\n        // If we haven't added a window listener for this event and it requires one,\n        // go ahead and add it\n        const windowListener = this.windowListeners[eventName];\n        if (windowListener && !windowListener.registered) {\n            this.addWindowListener(windowListener);\n        }\n        if (firstListener) {\n            this.sendRetainedArgumentsForEvent(eventName);\n        }\n        const remove = async () => this.removeListener(eventName, listenerFunc);\n        const p = Promise.resolve({ remove });\n        return p;\n    }\n    async removeAllListeners() {\n        this.listeners = {};\n        for (const listener in this.windowListeners) {\n            this.removeWindowListener(this.windowListeners[listener]);\n        }\n        this.windowListeners = {};\n    }\n    notifyListeners(eventName, data, retainUntilConsumed) {\n        const listeners = this.listeners[eventName];\n        if (!listeners) {\n            if (retainUntilConsumed) {\n                let args = this.retainedEventArguments[eventName];\n                if (!args) {\n                    args = [];\n                }\n                args.push(data);\n                this.retainedEventArguments[eventName] = args;\n            }\n            return;\n        }\n        listeners.forEach((listener) => listener(data));\n    }\n    hasListeners(eventName) {\n        var _a;\n        return !!((_a = this.listeners[eventName]) === null || _a === void 0 ? void 0 : _a.length);\n    }\n    registerWindowListener(windowEventName, pluginEventName) {\n        this.windowListeners[pluginEventName] = {\n            registered: false,\n            windowEventName,\n            pluginEventName,\n            handler: (event) => {\n                this.notifyListeners(pluginEventName, event);\n            },\n        };\n    }\n    unimplemented(msg = 'not implemented') {\n        return new Capacitor.Exception(msg, ExceptionCode.Unimplemented);\n    }\n    unavailable(msg = 'not available') {\n        return new Capacitor.Exception(msg, ExceptionCode.Unavailable);\n    }\n    async removeListener(eventName, listenerFunc) {\n        const listeners = this.listeners[eventName];\n        if (!listeners) {\n            return;\n        }\n        const index = listeners.indexOf(listenerFunc);\n        this.listeners[eventName].splice(index, 1);\n        // If there are no more listeners for this type of event,\n        // remove the window listener\n        if (!this.listeners[eventName].length) {\n            this.removeWindowListener(this.windowListeners[eventName]);\n        }\n    }\n    addWindowListener(handle) {\n        window.addEventListener(handle.windowEventName, handle.handler);\n        handle.registered = true;\n    }\n    removeWindowListener(handle) {\n        if (!handle) {\n            return;\n        }\n        window.removeEventListener(handle.windowEventName, handle.handler);\n        handle.registered = false;\n    }\n    sendRetainedArgumentsForEvent(eventName) {\n        const args = this.retainedEventArguments[eventName];\n        if (!args) {\n            return;\n        }\n        delete this.retainedEventArguments[eventName];\n        args.forEach((arg) => {\n            this.notifyListeners(eventName, arg);\n        });\n    }\n}\n//# sourceMappingURL=web-plugin.js.map", "import { registerPlugin } from './global';\nimport { WebPlugin } from './web-plugin';\nexport const WebView = /*#__PURE__*/ registerPlugin('WebView');\n/******** END WEB VIEW PLUGIN ********/\n/******** COOKIES PLUGIN ********/\n/**\n * Safely web encode a string value (inspired by js-cookie)\n * @param str The string value to encode\n */\nconst encode = (str) => encodeURIComponent(str)\n    .replace(/%(2[346B]|5E|60|7C)/g, decodeURIComponent)\n    .replace(/[()]/g, escape);\n/**\n * Safely web decode a string value (inspired by js-cookie)\n * @param str The string value to decode\n */\nconst decode = (str) => str.replace(/(%[\\dA-F]{2})+/gi, decodeURIComponent);\nexport class CapacitorCookiesPluginWeb extends WebPlugin {\n    async getCookies() {\n        const cookies = document.cookie;\n        const cookieMap = {};\n        cookies.split(';').forEach((cookie) => {\n            if (cookie.length <= 0)\n                return;\n            // Replace first \"=\" with CAP_COOKIE to prevent splitting on additional \"=\"\n            let [key, value] = cookie.replace(/=/, 'CAP_COOKIE').split('CAP_COOKIE');\n            key = decode(key).trim();\n            value = decode(value).trim();\n            cookieMap[key] = value;\n        });\n        return cookieMap;\n    }\n    async setCookie(options) {\n        try {\n            // Safely Encoded Key/Value\n            const encodedKey = encode(options.key);\n            const encodedValue = encode(options.value);\n            // Clean & sanitize options\n            const expires = `; expires=${(options.expires || '').replace('expires=', '')}`; // Default is \"; expires=\"\n            const path = (options.path || '/').replace('path=', ''); // Default is \"path=/\"\n            const domain = options.url != null && options.url.length > 0 ? `domain=${options.url}` : '';\n            document.cookie = `${encodedKey}=${encodedValue || ''}${expires}; path=${path}; ${domain};`;\n        }\n        catch (error) {\n            return Promise.reject(error);\n        }\n    }\n    async deleteCookie(options) {\n        try {\n            document.cookie = `${options.key}=; Max-Age=0`;\n        }\n        catch (error) {\n            return Promise.reject(error);\n        }\n    }\n    async clearCookies() {\n        try {\n            const cookies = document.cookie.split(';') || [];\n            for (const cookie of cookies) {\n                document.cookie = cookie.replace(/^ +/, '').replace(/=.*/, `=;expires=${new Date().toUTCString()};path=/`);\n            }\n        }\n        catch (error) {\n            return Promise.reject(error);\n        }\n    }\n    async clearAllCookies() {\n        try {\n            await this.clearCookies();\n        }\n        catch (error) {\n            return Promise.reject(error);\n        }\n    }\n}\nexport const CapacitorCookies = registerPlugin('CapacitorCookies', {\n    web: () => new CapacitorCookiesPluginWeb(),\n});\n// UTILITY FUNCTIONS\n/**\n * Read in a Blob value and return it as a base64 string\n * @param blob The blob value to convert to a base64 string\n */\nexport const readBlobAsBase64 = async (blob) => new Promise((resolve, reject) => {\n    const reader = new FileReader();\n    reader.onload = () => {\n        const base64String = reader.result;\n        // remove prefix \"data:application/pdf;base64,\"\n        resolve(base64String.indexOf(',') >= 0 ? base64String.split(',')[1] : base64String);\n    };\n    reader.onerror = (error) => reject(error);\n    reader.readAsDataURL(blob);\n});\n/**\n * Normalize an HttpHeaders map by lowercasing all of the values\n * @param headers The HttpHeaders object to normalize\n */\nconst normalizeHttpHeaders = (headers = {}) => {\n    const originalKeys = Object.keys(headers);\n    const loweredKeys = Object.keys(headers).map((k) => k.toLocaleLowerCase());\n    const normalized = loweredKeys.reduce((acc, key, index) => {\n        acc[key] = headers[originalKeys[index]];\n        return acc;\n    }, {});\n    return normalized;\n};\n/**\n * Builds a string of url parameters that\n * @param params A map of url parameters\n * @param shouldEncode true if you should encodeURIComponent() the values (true by default)\n */\nconst buildUrlParams = (params, shouldEncode = true) => {\n    if (!params)\n        return null;\n    const output = Object.entries(params).reduce((accumulator, entry) => {\n        const [key, value] = entry;\n        let encodedValue;\n        let item;\n        if (Array.isArray(value)) {\n            item = '';\n            value.forEach((str) => {\n                encodedValue = shouldEncode ? encodeURIComponent(str) : str;\n                item += `${key}=${encodedValue}&`;\n            });\n            // last character will always be \"&\" so slice it off\n            item.slice(0, -1);\n        }\n        else {\n            encodedValue = shouldEncode ? encodeURIComponent(value) : value;\n            item = `${key}=${encodedValue}`;\n        }\n        return `${accumulator}&${item}`;\n    }, '');\n    // Remove initial \"&\" from the reduce\n    return output.substr(1);\n};\n/**\n * Build the RequestInit object based on the options passed into the initial request\n * @param options The Http plugin options\n * @param extra Any extra RequestInit values\n */\nexport const buildRequestInit = (options, extra = {}) => {\n    const output = Object.assign({ method: options.method || 'GET', headers: options.headers }, extra);\n    // Get the content-type\n    const headers = normalizeHttpHeaders(options.headers);\n    const type = headers['content-type'] || '';\n    // If body is already a string, then pass it through as-is.\n    if (typeof options.data === 'string') {\n        output.body = options.data;\n    }\n    // Build request initializers based off of content-type\n    else if (type.includes('application/x-www-form-urlencoded')) {\n        const params = new URLSearchParams();\n        for (const [key, value] of Object.entries(options.data || {})) {\n            params.set(key, value);\n        }\n        output.body = params.toString();\n    }\n    else if (type.includes('multipart/form-data') || options.data instanceof FormData) {\n        const form = new FormData();\n        if (options.data instanceof FormData) {\n            options.data.forEach((value, key) => {\n                form.append(key, value);\n            });\n        }\n        else {\n            for (const key of Object.keys(options.data)) {\n                form.append(key, options.data[key]);\n            }\n        }\n        output.body = form;\n        const headers = new Headers(output.headers);\n        headers.delete('content-type'); // content-type will be set by `window.fetch` to includy boundary\n        output.headers = headers;\n    }\n    else if (type.includes('application/json') || typeof options.data === 'object') {\n        output.body = JSON.stringify(options.data);\n    }\n    return output;\n};\n// WEB IMPLEMENTATION\nexport class CapacitorHttpPluginWeb extends WebPlugin {\n    /**\n     * Perform an Http request given a set of options\n     * @param options Options to build the HTTP request\n     */\n    async request(options) {\n        const requestInit = buildRequestInit(options, options.webFetchExtra);\n        const urlParams = buildUrlParams(options.params, options.shouldEncodeUrlParams);\n        const url = urlParams ? `${options.url}?${urlParams}` : options.url;\n        const response = await fetch(url, requestInit);\n        const contentType = response.headers.get('content-type') || '';\n        // Default to 'text' responseType so no parsing happens\n        let { responseType = 'text' } = response.ok ? options : {};\n        // If the response content-type is json, force the response to be json\n        if (contentType.includes('application/json')) {\n            responseType = 'json';\n        }\n        let data;\n        let blob;\n        switch (responseType) {\n            case 'arraybuffer':\n            case 'blob':\n                blob = await response.blob();\n                data = await readBlobAsBase64(blob);\n                break;\n            case 'json':\n                data = await response.json();\n                break;\n            case 'document':\n            case 'text':\n            default:\n                data = await response.text();\n        }\n        // Convert fetch headers to Capacitor HttpHeaders\n        const headers = {};\n        response.headers.forEach((value, key) => {\n            headers[key] = value;\n        });\n        return {\n            data,\n            headers,\n            status: response.status,\n            url: response.url,\n        };\n    }\n    /**\n     * Perform an Http GET request given a set of options\n     * @param options Options to build the HTTP request\n     */\n    async get(options) {\n        return this.request(Object.assign(Object.assign({}, options), { method: 'GET' }));\n    }\n    /**\n     * Perform an Http POST request given a set of options\n     * @param options Options to build the HTTP request\n     */\n    async post(options) {\n        return this.request(Object.assign(Object.assign({}, options), { method: 'POST' }));\n    }\n    /**\n     * Perform an Http PUT request given a set of options\n     * @param options Options to build the HTTP request\n     */\n    async put(options) {\n        return this.request(Object.assign(Object.assign({}, options), { method: 'PUT' }));\n    }\n    /**\n     * Perform an Http PATCH request given a set of options\n     * @param options Options to build the HTTP request\n     */\n    async patch(options) {\n        return this.request(Object.assign(Object.assign({}, options), { method: 'PATCH' }));\n    }\n    /**\n     * Perform an Http DELETE request given a set of options\n     * @param options Options to build the HTTP request\n     */\n    async delete(options) {\n        return this.request(Object.assign(Object.assign({}, options), { method: 'DELETE' }));\n    }\n}\nexport const CapacitorHttp = registerPlugin('CapacitorHttp', {\n    web: () => new CapacitorHttpPluginWeb(),\n});\n/******** END HTTP PLUGIN ********/\n//# sourceMappingURL=core-plugins.js.map"], "names": ["ExceptionCode", "exports", "CapacitorException", "Error", "constructor", "message", "code", "data", "super", "this", "createCapacitor", "win", "capCustomPlatform", "CapacitorCustomPlatform", "cap", "Capacitor", "Plugins", "getPlatform", "name", "_a", "_b", "androidBridge", "webkit", "messageHandlers", "bridge", "getPlatformId", "getPluginHeader", "pluginName", "PluginHeaders", "find", "h", "registeredPlugins", "Map", "convertFileSrc", "filePath", "handleError", "err", "console", "error", "isNativePlatform", "isPluginAvailable", "plugin", "get", "platforms", "has", "registerPlugin", "jsImplementations", "registeredPlugin", "warn", "proxy", "platform", "pluginHeader", "jsImplementation", "createPluginMethodWrapper", "prop", "remove", "wrapper", "args", "p", "async", "loadPluginImplementation", "then", "impl", "fn", "bind", "Unimplemented", "methodHeader", "methods", "m", "rtype", "options", "nativePromise", "toString", "callback", "nativeCallback", "createPluginMethod", "Object", "defineProperty", "value", "writable", "configurable", "addListener", "removeListener", "addListenerNative", "eventName", "call", "callbackId", "Promise", "resolve", "Proxy", "_", "set", "Set", "keys", "Exception", "DEBUG", "isLoggingEnabled", "initCapacitorGlobal", "globalThis", "self", "window", "global", "WebPlugin", "listeners", "retainedEventArguments", "windowListeners", "listenerFunc", "firstListener", "push", "windowListener", "registered", "addWindowListener", "sendRetainedArgumentsForEvent", "removeAllListeners", "listener", "removeWindowListener", "notifyListeners", "retainUntilConsumed", "for<PERSON>ach", "hasListeners", "length", "registerWindowListener", "windowEventName", "pluginEventName", "handler", "event", "unimplemented", "msg", "unavailable", "Unavailable", "index", "indexOf", "splice", "handle", "addEventListener", "removeEventListener", "arg", "WebView", "encode", "str", "encodeURIComponent", "replace", "decodeURIComponent", "escape", "decode", "CapacitorCookiesPluginWeb", "getCookies", "cookies", "document", "cookie", "cookieMap", "split", "key", "trim", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "encodedValue", "expires", "path", "domain", "url", "reject", "deleteC<PERSON>ie", "clearCookies", "Date", "toUTCString", "clearAllCookies", "CapacitorCookies", "web", "buildRequestInit", "extra", "output", "assign", "method", "headers", "type", "originalKeys", "map", "k", "toLocaleLowerCase", "reduce", "acc", "normalizeHttpHeaders", "body", "includes", "params", "URLSearchParams", "entries", "FormData", "form", "append", "Headers", "delete", "JSON", "stringify", "CapacitorHttpPluginWeb", "request", "requestInit", "webFetchExtra", "urlParams", "shouldEncode", "accumulator", "entry", "item", "Array", "isArray", "slice", "substr", "buildUrlParams", "shouldEncodeUrlParams", "response", "fetch", "contentType", "blob", "responseType", "ok", "reader", "FileReader", "onload", "base64String", "result", "onerror", "readAsDataURL", "readBlobAsBase64", "json", "text", "status", "post", "put", "patch", "CapacitorHttp"], "mappings": ";8CACA,IAAWA,EADcC,EAAAD,mBAAA,GACdA,EAgBRA,kBAAkBA,EAAAA,cAAgB,CAAE,IATN,cAAI,gBAQjCA,EAA2B,YAAI,cAE5B,MAAME,UAA2BC,MACpC,WAAAC,CAAYC,EAASC,EAAMC,GACvBC,MAAMH,GACNI,KAAKJ,QAAUA,EACfI,KAAKH,KAAOA,EACZG,KAAKF,KAAOA,CACf,EAEE,MCzBMG,EAAmBC,IAC5B,MAAMC,EAAoBD,EAAIE,yBAA2B,KACnDC,EAAMH,EAAII,WAAa,GACvBC,EAAWF,EAAIE,QAAUF,EAAIE,SAAW,CAAA,EACxCC,EAAc,IACa,OAAtBL,EAA6BA,EAAkBM,KDoBjC,CAACP,IAC1B,IAAIQ,EAAIC,EACR,OAAIT,aAAiC,EAASA,EAAIU,eACvC,WAE6H,QAA9HD,EAAqE,QAA/DD,EAAKR,aAAiC,EAASA,EAAIW,cAA2B,IAAPH,OAAgB,EAASA,EAAGI,uBAAoC,IAAPH,OAAgB,EAASA,EAAGI,QACjK,MAGA,OC7BsDC,CAAcd,GAezEe,EAAmBC,IAAiB,IAAIR,EAAI,OAAoC,QAA5BA,EAAKL,EAAIc,qBAAkC,IAAPT,OAAgB,EAASA,EAAGU,KAAMC,GAAMA,EAAEZ,OAASS,IAE3II,EAAoB,IAAIC,IAmI9B,OAXKlB,EAAImB,iBACLnB,EAAImB,eAAkBC,GAAaA,GAEvCpB,EAAIG,YAAcA,EAClBH,EAAIqB,YA7HiBC,GAAQzB,EAAI0B,QAAQC,MAAMF,GA8H/CtB,EAAIyB,iBA5IqB,IAAwB,QAAlBtB,IA6I/BH,EAAI0B,kBA5IuBb,IACvB,MAAMc,EAASV,EAAkBW,IAAIf,GACrC,SAAIc,aAAuC,EAASA,EAAOE,UAAUC,IAAI3B,SAIrES,EAAgBC,IAuIxBb,EAAI+B,eA9HmB,CAAClB,EAAYmB,EAAoB,CAAA,KACpD,MAAMC,EAAmBhB,EAAkBW,IAAIf,GAC/C,GAAIoB,EAEA,OADAV,QAAQW,KAAK,qBAAqBrB,yDAC3BoB,EAAiBE,MAE5B,MAAMC,EAAWjC,IACXkC,EAAezB,EAAgBC,GACrC,IAAIyB,EACJ,MAsCMC,EAA6BC,IAC/B,IAAIC,EACJ,MAAMC,EAAU,IAAIC,KAChB,MAAMC,EAzCmBC,YACxBP,GAAoBF,KAAYJ,EACjCM,EAEWA,EADgC,mBAAhCN,EAAkBI,SACOJ,EAAkBI,KACxBJ,EAAkBI,GAErB,OAAtBtC,IAA+BwC,GAAoB,QAASN,IACjEM,EAEWA,EAD6B,mBAA7BN,EAAuB,UACEA,EAAuB,MAC7BA,EAAuB,KAElDM,GA4BOQ,GAA2BC,KAAMC,IACvC,MAAMC,EA3BS,EAACD,EAAMR,KAC9B,IAAInC,EAAIC,EACR,IAAI+B,EAcC,IAAIW,EACL,OAA6B,QAArB1C,EAAK0C,EAAKR,UAA0B,IAAPlC,OAAgB,EAASA,EAAG4C,KAAKF,GAGtE,MAAM,IAAI5D,EAAmB,IAAIyB,mCAA4CuB,IAAYlD,gBAAciE,cAC1G,CAnBiB,CACd,MAAMC,EAAef,aAAmD,EAASA,EAAagB,QAAQtC,KAAMuC,GAAMd,IAASc,EAAElD,MAC7H,GAAIgD,EACA,MAA2B,YAAvBA,EAAaG,MACLC,GAAYxD,EAAIyD,cAAc5C,EAAY2B,EAAKkB,WAAYF,GAG5D,CAACA,EAASG,IAAa3D,EAAI4D,eAAe/C,EAAY2B,EAAKkB,WAAYF,EAASG,GAG1F,GAAIX,EACL,OAA6B,QAArB3C,EAAK2C,EAAKR,UAA0B,IAAPnC,OAAgB,EAASA,EAAG6C,KAAKF,EAE7E,GAYkBa,CAAmBb,EAAMR,GACpC,GAAIS,EAAI,CACJ,MAAML,EAAIK,KAAMN,GAEhB,OADAF,EAASG,aAA6B,EAASA,EAAEH,OAC1CG,CACV,CAEG,MAAM,IAAIxD,EAAmB,IAAIyB,KAAc2B,8BAAiCJ,IAAYlD,EAAaA,cAACiE,iBAMlH,MAHa,gBAATX,IACAI,EAAEH,OAASI,SAAYJ,KAEpBG,GASX,OANAF,EAAQgB,SAAW,IAAM,GAAGlB,EAAKkB,oCACjCI,OAAOC,eAAerB,EAAS,OAAQ,CACnCsB,MAAOxB,EACPyB,UAAU,EACVC,cAAc,IAEXxB,GAELyB,EAAc5B,EAA0B,eACxC6B,EAAiB7B,EAA0B,kBAC3C8B,EAAoB,CAACC,EAAWX,KAClC,MAAMY,EAAOJ,EAAY,CAAEG,aAAaX,GAClClB,EAASI,UACX,MAAM2B,QAAmBD,EACzBH,EAAe,CACXE,YACAE,cACDb,IAEDf,EAAI,IAAI6B,QAASC,GAAYH,EAAKxB,KAAK,IAAM2B,EAAQ,CAAEjC,aAK7D,OAJAG,EAAEH,OAASI,UACPtB,QAAQW,KAAK,4DACPO,KAEHG,GAELT,EAAQ,IAAIwC,MAAM,GAAI,CACxB,GAAA/C,CAAIgD,EAAGpC,GACH,OAAQA,GAEJ,IAAK,WACD,OACJ,IAAK,SACD,MAAO,KAAA,CAAS,GACpB,IAAK,cACD,OAAOH,EAAegC,EAAoBF,EAC9C,IAAK,iBACD,OAAOC,EACX,QACI,OAAO7B,EAA0BC,GAE5C,IAQL,OANAtC,EAAQW,GAAcsB,EACtBlB,EAAkB4D,IAAIhE,EAAY,CAC9BT,KAAMS,EACNsB,QACAN,UAAW,IAAIiD,IAAI,IAAIhB,OAAOiB,KAAK/C,MAAwBK,EAAe,CAACD,GAAY,OAEpFD,GAWXnC,EAAIgF,UAAY5F,EAChBY,EAAIiF,QAAUjF,EAAIiF,MAClBjF,EAAIkF,mBAAqBlF,EAAIkF,iBACtBlF,GCzJEC,ED2JsB,CAACJ,GAASA,EAAII,UAAYL,EAAgBC,GC3JtCsF,CAA0C,oBAAfC,WAC5DA,WACgB,oBAATC,KACHA,KACkB,oBAAXC,OACHA,OACkB,oBAAXC,OACHA,OACA,IACLxD,EAAiB9B,EAAU8B,eCLjC,MAAMyD,EACT,WAAAlG,GACIK,KAAK8F,UAAY,GACjB9F,KAAK+F,uBAAyB,GAC9B/F,KAAKgG,gBAAkB,EAC1B,CACD,WAAAxB,CAAYG,EAAWsB,GACnB,IAAIC,GAAgB,EACFlG,KAAK8F,UAAUnB,KAE7B3E,KAAK8F,UAAUnB,GAAa,GAC5BuB,GAAgB,GAEpBlG,KAAK8F,UAAUnB,GAAWwB,KAAKF,GAG/B,MAAMG,EAAiBpG,KAAKgG,gBAAgBrB,GACxCyB,IAAmBA,EAAeC,YAClCrG,KAAKsG,kBAAkBF,GAEvBF,GACAlG,KAAKuG,8BAA8B5B,GAIvC,OADUG,QAAQC,QAAQ,CAAEjC,OADbI,SAAYlD,KAAKyE,eAAeE,EAAWsB,IAG7D,CACD,wBAAMO,GACFxG,KAAK8F,UAAY,GACjB,IAAK,MAAMW,KAAYzG,KAAKgG,gBACxBhG,KAAK0G,qBAAqB1G,KAAKgG,gBAAgBS,IAEnDzG,KAAKgG,gBAAkB,EAC1B,CACD,eAAAW,CAAgBhC,EAAW7E,EAAM8G,GAC7B,MAAMd,EAAY9F,KAAK8F,UAAUnB,GACjC,GAAKmB,EAWLA,EAAUe,QAASJ,GAAaA,EAAS3G,SAVrC,GAAI8G,EAAqB,CACrB,IAAI5D,EAAOhD,KAAK+F,uBAAuBpB,GAClC3B,IACDA,EAAO,IAEXA,EAAKmD,KAAKrG,GACVE,KAAK+F,uBAAuBpB,GAAa3B,CAC5C,CAIR,CACD,YAAA8D,CAAanC,GACT,IAAIjE,EACJ,SAA+C,QAApCA,EAAKV,KAAK8F,UAAUnB,UAA+B,IAAPjE,OAAgB,EAASA,EAAGqG,OACtF,CACD,sBAAAC,CAAuBC,EAAiBC,GACpClH,KAAKgG,gBAAgBkB,GAAmB,CACpCb,YAAY,EACZY,kBACAC,kBACAC,QAAUC,IACNpH,KAAK2G,gBAAgBO,EAAiBE,IAGjD,CACD,aAAAC,CAAcC,EAAM,mBAChB,OAAO,IAAIhH,EAAU+E,UAAUiC,EAAK/H,EAAaA,cAACiE,cACrD,CACD,WAAA+D,CAAYD,EAAM,iBACd,OAAO,IAAIhH,EAAU+E,UAAUiC,EAAK/H,EAAaA,cAACiI,YACrD,CACD,oBAAM/C,CAAeE,EAAWsB,GAC5B,MAAMH,EAAY9F,KAAK8F,UAAUnB,GACjC,IAAKmB,EACD,OAEJ,MAAM2B,EAAQ3B,EAAU4B,QAAQzB,GAChCjG,KAAK8F,UAAUnB,GAAWgD,OAAOF,EAAO,GAGnCzH,KAAK8F,UAAUnB,GAAWoC,QAC3B/G,KAAK0G,qBAAqB1G,KAAKgG,gBAAgBrB,GAEtD,CACD,iBAAA2B,CAAkBsB,GACdjC,OAAOkC,iBAAiBD,EAAOX,gBAAiBW,EAAOT,SACvDS,EAAOvB,YAAa,CACvB,CACD,oBAAAK,CAAqBkB,GACZA,IAGLjC,OAAOmC,oBAAoBF,EAAOX,gBAAiBW,EAAOT,SAC1DS,EAAOvB,YAAa,EACvB,CACD,6BAAAE,CAA8B5B,GAC1B,MAAM3B,EAAOhD,KAAK+F,uBAAuBpB,GACpC3B,WAGEhD,KAAK+F,uBAAuBpB,GACnC3B,EAAK6D,QAASkB,IACV/H,KAAK2G,gBAAgBhC,EAAWoD,KAEvC,ECzGO,MAACC,EAAwB5F,EAAe,WAO9C6F,EAAUC,GAAQC,mBAAmBD,GACtCE,QAAQ,uBAAwBC,oBAChCD,QAAQ,QAASE,QAKhBC,EAAUL,GAAQA,EAAIE,QAAQ,mBAAoBC,oBACjD,MAAMG,UAAkC3C,EAC3C,gBAAM4C,GACF,MAAMC,EAAUC,SAASC,OACnBC,EAAY,CAAA,EAUlB,OATAH,EAAQI,MAAM,KAAKjC,QAAS+B,IACxB,GAAIA,EAAO7B,QAAU,EACjB,OAEJ,IAAKgC,EAAK1E,GAASuE,EAAOR,QAAQ,IAAK,cAAcU,MAAM,cAC3DC,EAAMR,EAAOQ,GAAKC,OAClB3E,EAAQkE,EAAOlE,GAAO2E,OACtBH,EAAUE,GAAO1E,IAEdwE,CACV,CACD,eAAMI,CAAUpF,GACZ,IAEI,MAAMqF,EAAajB,EAAOpE,EAAQkF,KAC5BI,EAAelB,EAAOpE,EAAQQ,OAE9B+E,EAAU,cAAcvF,EAAQuF,SAAW,IAAIhB,QAAQ,WAAY,MACnEiB,GAAQxF,EAAQwF,MAAQ,KAAKjB,QAAQ,QAAS,IAC9CkB,EAAwB,MAAfzF,EAAQ0F,KAAe1F,EAAQ0F,IAAIxC,OAAS,EAAI,UAAUlD,EAAQ0F,MAAQ,GACzFZ,SAASC,OAAS,GAAGM,KAAcC,GAAgB,KAAKC,WAAiBC,MAASC,IACrF,CACD,MAAOzH,GACH,OAAOiD,QAAQ0E,OAAO3H,EACzB,CACJ,CACD,kBAAM4H,CAAa5F,GACf,IACI8E,SAASC,OAAS,GAAG/E,EAAQkF,iBAChC,CACD,MAAOlH,GACH,OAAOiD,QAAQ0E,OAAO3H,EACzB,CACJ,CACD,kBAAM6H,GACF,IACI,MAAMhB,EAAUC,SAASC,OAAOE,MAAM,MAAQ,GAC9C,IAAK,MAAMF,KAAUF,EACjBC,SAASC,OAASA,EAAOR,QAAQ,MAAO,IAAIA,QAAQ,MAAO,cAAa,IAAIuB,MAAOC,uBAE1F,CACD,MAAO/H,GACH,OAAOiD,QAAQ0E,OAAO3H,EACzB,CACJ,CACD,qBAAMgI,GACF,UACU7J,KAAK0J,cACd,CACD,MAAO7H,GACH,OAAOiD,QAAQ0E,OAAO3H,EACzB,CACJ,EAEO,MAACiI,EAAmB1H,EAAe,mBAAoB,CAC/D2H,IAAK,IAAM,IAAIvB,IAiENwB,EAAmB,CAACnG,EAASoG,EAAQ,MAC9C,MAAMC,EAAS/F,OAAOgG,OAAO,CAAEC,OAAQvG,EAAQuG,QAAU,MAAOC,QAASxG,EAAQwG,SAAWJ,GAGtFK,EAhDmB,EAACD,EAAU,MACpC,MAAME,EAAepG,OAAOiB,KAAKiF,GAMjC,OALoBlG,OAAOiB,KAAKiF,GAASG,IAAKC,GAAMA,EAAEC,qBACvBC,OAAO,CAACC,EAAK7B,EAAKtB,KAC7CmD,EAAI7B,GAAOsB,EAAQE,EAAa9C,IACzBmD,GACR,CAAE,IAyCWC,CAAqBhH,EAAQwG,SACxB,iBAAmB,GAExC,GAA4B,iBAAjBxG,EAAQ/D,KACfoK,EAAOY,KAAOjH,EAAQ/D,UAGrB,GAAIwK,EAAKS,SAAS,qCAAsC,CACzD,MAAMC,EAAS,IAAIC,gBACnB,IAAK,MAAOlC,EAAK1E,KAAUF,OAAO+G,QAAQrH,EAAQ/D,MAAQ,CAAA,GACtDkL,EAAO9F,IAAI6D,EAAK1E,GAEpB6F,EAAOY,KAAOE,EAAOjH,UACxB,MACI,GAAIuG,EAAKS,SAAS,wBAA0BlH,EAAQ/D,gBAAgBqL,SAAU,CAC/E,MAAMC,EAAO,IAAID,SACjB,GAAItH,EAAQ/D,gBAAgBqL,SACxBtH,EAAQ/D,KAAK+G,QAAQ,CAACxC,EAAO0E,KACzBqC,EAAKC,OAAOtC,EAAK1E,UAIrB,IAAK,MAAM0E,KAAO5E,OAAOiB,KAAKvB,EAAQ/D,MAClCsL,EAAKC,OAAOtC,EAAKlF,EAAQ/D,KAAKiJ,IAGtCmB,EAAOY,KAAOM,EACd,MAAMf,EAAU,IAAIiB,QAAQpB,EAAOG,SACnCA,EAAQkB,OAAO,gBACfrB,EAAOG,QAAUA,CACpB,MACQC,EAAKS,SAAS,qBAA+C,iBAAjBlH,EAAQ/D,QACzDoK,EAAOY,KAAOU,KAAKC,UAAU5H,EAAQ/D,OAEzC,OAAOoK,GAGJ,MAAMwB,UAA+B7F,EAKxC,aAAM8F,CAAQ9H,GACV,MAAM+H,EAAc5B,EAAiBnG,EAASA,EAAQgI,eAChDC,EA7ES,EAACd,EAAQe,GAAe,IACtCf,EAEU7G,OAAO+G,QAAQF,GAAQL,OAAO,CAACqB,EAAaC,KACvD,MAAOlD,EAAK1E,GAAS4H,EACrB,IAAI9C,EACA+C,EAcJ,OAbIC,MAAMC,QAAQ/H,IACd6H,EAAO,GACP7H,EAAMwC,QAASqB,IACXiB,EAAe4C,EAAe5D,mBAAmBD,GAAOA,EACxDgE,GAAQ,GAAGnD,KAAOI,OAGtB+C,EAAKG,MAAM,GAAI,KAGflD,EAAe4C,EAAe5D,mBAAmB9D,GAASA,EAC1D6H,EAAO,GAAGnD,KAAOI,KAEd,GAAG6C,KAAeE,KAC1B,IAEWI,OAAO,GArBV,KA2EWC,CAAe1I,EAAQmH,OAAQnH,EAAQ2I,uBACnDjD,EAAMuC,EAAY,GAAGjI,EAAQ0F,OAAOuC,IAAcjI,EAAQ0F,IAC1DkD,QAAiBC,MAAMnD,EAAKqC,GAC5Be,EAAcF,EAASpC,QAAQpI,IAAI,iBAAmB,GAE5D,IAKInC,EACA8M,GANAC,aAAEA,EAAe,QAAWJ,EAASK,GAAKjJ,EAAU,GAOxD,OALI8I,EAAY5B,SAAS,sBACrB8B,EAAe,QAIXA,GACJ,IAAK,cACL,IAAK,OACDD,QAAaH,EAASG,OACtB9M,OAzHgBoD,OAAO0J,GAAS,IAAI9H,QAAQ,CAACC,EAASyE,KAClE,MAAMuD,EAAS,IAAIC,WACnBD,EAAOE,OAAS,KACZ,MAAMC,EAAeH,EAAOI,OAE5BpI,EAAQmI,EAAaxF,QAAQ,MAAQ,EAAIwF,EAAapE,MAAM,KAAK,GAAKoE,IAE1EH,EAAOK,QAAWvL,GAAU2H,EAAO3H,GACnCkL,EAAOM,cAAcT,KAiHIU,CAAiBV,GAC9B,MACJ,IAAK,OACD9M,QAAa2M,EAASc,OACtB,MAGJ,QACIzN,QAAa2M,EAASe,OAG9B,MAAMnD,EAAU,CAAA,EAIhB,OAHAoC,EAASpC,QAAQxD,QAAQ,CAACxC,EAAO0E,KAC7BsB,EAAQtB,GAAO1E,IAEZ,CACHvE,OACAuK,UACAoD,OAAQhB,EAASgB,OACjBlE,IAAKkD,EAASlD,IAErB,CAKD,SAAMtH,CAAI4B,GACN,OAAO7D,KAAK2L,QAAQxH,OAAOgG,OAAOhG,OAAOgG,OAAO,CAAE,EAAEtG,GAAU,CAAEuG,OAAQ,QAC3E,CAKD,UAAMsD,CAAK7J,GACP,OAAO7D,KAAK2L,QAAQxH,OAAOgG,OAAOhG,OAAOgG,OAAO,CAAE,EAAEtG,GAAU,CAAEuG,OAAQ,SAC3E,CAKD,SAAMuD,CAAI9J,GACN,OAAO7D,KAAK2L,QAAQxH,OAAOgG,OAAOhG,OAAOgG,OAAO,CAAE,EAAEtG,GAAU,CAAEuG,OAAQ,QAC3E,CAKD,WAAMwD,CAAM/J,GACR,OAAO7D,KAAK2L,QAAQxH,OAAOgG,OAAOhG,OAAOgG,OAAO,CAAE,EAAEtG,GAAU,CAAEuG,OAAQ,UAC3E,CAKD,YAAM,CAAOvG,GACT,OAAO7D,KAAK2L,QAAQxH,OAAOgG,OAAOhG,OAAOgG,OAAO,CAAE,EAAEtG,GAAU,CAAEuG,OAAQ,WAC3E,EAEO,MAACyD,EAAgBzL,EAAe,gBAAiB,CACzD2H,IAAK,IAAM,IAAI2B"}