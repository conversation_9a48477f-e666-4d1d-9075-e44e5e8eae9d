{"version": 3, "file": "definitions.js", "sourceRoot": "", "sources": ["../../src/definitions.ts"], "names": [], "mappings": "", "sourcesContent": ["export interface ShareOptions {\n  /**\n   * Set a title for any message. This will be the subject\n   * if sharing to email\n   *\n   * @since 1.0.0\n   */\n  title?: string;\n\n  /**\n   * Set some text to share\n   *\n   * @since 1.0.0\n   */\n  text?: string;\n\n  /**\n   * Set a URL to share, can be http, https or file:// URL\n   *\n   * @since 1.0.0\n   */\n  url?: string;\n\n  /**\n   * Array of file:// URLs of the files to be shared.\n   * Only supported on iOS and Android.\n   *\n   * @since 4.1.0\n   */\n  files?: string[];\n\n  /**\n   * Set a title for the share modal.\n   * This option is only supported on Android.\n   *\n   * @since 1.0.0\n   */\n  dialogTitle?: string;\n}\n\nexport interface ShareResult {\n  /**\n   * Identifier of the app that received the share action.\n   * Can be an empty string in some cases.\n   *\n   * On web it will be undefined.\n   *\n   * @since 1.0.0\n   */\n  activityType?: string;\n}\n\nexport interface CanShareResult {\n  /**\n   * Whether sharing is supported or not.\n   *\n   * @since 1.1.0\n   */\n  value: boolean;\n}\n\nexport interface SharePlugin {\n  /**\n   * Check if sharing is supported.\n   *\n   * @since 1.1.0\n   */\n  canShare(): Promise<CanShareResult>;\n\n  /**\n   * Show a Share modal for sharing content with other apps\n   *\n   * @since 1.0.0\n   */\n  share(options: ShareOptions): Promise<ShareResult>;\n}\n"]}