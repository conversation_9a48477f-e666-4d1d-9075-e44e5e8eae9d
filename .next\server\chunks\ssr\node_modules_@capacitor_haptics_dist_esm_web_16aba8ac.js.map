{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "web.js", "sourceRoot": "", "sources": ["file:///C:/QuantAxiom/Products/LifeManager/node_modules/%40capacitor/haptics/src/web.ts"], "sourcesContent": ["import { WebPlugin } from '@capacitor/core';\n\nimport { ImpactStyle, NotificationType } from './definitions';\nimport type {\n  HapticsPlugin,\n  ImpactOptions,\n  NotificationOptions,\n  VibrateOptions,\n} from './definitions';\n\nexport class HapticsWeb extends WebPlugin implements HapticsPlugin {\n  selectionStarted = false;\n\n  async impact(options?: ImpactOptions): Promise<void> {\n    const pattern = this.patternForImpact(options?.style);\n    this.vibrateWithPattern(pattern);\n  }\n\n  async notification(options?: NotificationOptions): Promise<void> {\n    const pattern = this.patternForNotification(options?.type);\n    this.vibrateWithPattern(pattern);\n  }\n\n  async vibrate(options?: VibrateOptions): Promise<void> {\n    const duration = options?.duration || 300;\n    this.vibrateWithPattern([duration]);\n  }\n\n  async selectionStart(): Promise<void> {\n    this.selectionStarted = true;\n  }\n\n  async selectionChanged(): Promise<void> {\n    if (this.selectionStarted) {\n      this.vibrateWithPattern([70]);\n    }\n  }\n\n  async selectionEnd(): Promise<void> {\n    this.selectionStarted = false;\n  }\n\n  private patternForImpact(style: ImpactStyle = ImpactStyle.Heavy): number[] {\n    if (style === ImpactStyle.Medium) {\n      return [43];\n    } else if (style === ImpactStyle.Light) {\n      return [20];\n    }\n    return [61];\n  }\n\n  private patternForNotification(\n    type: NotificationType = NotificationType.Success,\n  ): number[] {\n    if (type === NotificationType.Warning) {\n      return [30, 40, 30, 50, 60];\n    } else if (type === NotificationType.Error) {\n      return [27, 45, 50];\n    }\n    return [35, 65, 21];\n  }\n\n  private vibrateWithPattern(pattern: number[]) {\n    if (navigator.vibrate) {\n      navigator.vibrate(pattern);\n    } else {\n      throw this.unavailable('Browser does not support the vibrate API');\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAE5C,OAAO,EAAE,WAAW,EAAE,gBAAgB,EAAE,MAAM,eAAe,CAAC;;;AAQxD,MAAO,UAAW,8JAAQ,YAAS;IAAzC,aAAA;;QACE,IAAA,CAAA,gBAAgB,GAAG,KAAK,CAAC;IA0D3B,CAAC;IAxDC,KAAK,CAAC,MAAM,CAAC,OAAuB,EAAA;QAClC,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,KAAK,CAAC,CAAC;QACtD,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;IACnC,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,OAA6B,EAAA;QAC9C,MAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,IAAI,CAAC,CAAC;QAC3D,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;IACnC,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,OAAwB,EAAA;QACpC,MAAM,QAAQ,GAAG,CAAA,OAAO,KAAA,QAAP,OAAO,KAAA,KAAA,IAAA,KAAA,IAAP,OAAO,CAAE,QAAQ,KAAI,GAAG,CAAC;QAC1C,IAAI,CAAC,kBAAkB,CAAC;YAAC,QAAQ;SAAC,CAAC,CAAC;IACtC,CAAC;IAED,KAAK,CAAC,cAAc,GAAA;QAClB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;IAC/B,CAAC;IAED,KAAK,CAAC,gBAAgB,GAAA;QACpB,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,IAAI,CAAC,kBAAkB,CAAC;gBAAC,EAAE;aAAC,CAAC,CAAC;SAC/B;IACH,CAAC;IAED,KAAK,CAAC,YAAY,GAAA;QAChB,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;IAChC,CAAC;IAEO,gBAAgB,CAAC,6KAAqB,cAAW,CAAC,KAAK,EAAA;QAC7D,IAAI,KAAK,0KAAK,cAAW,CAAC,MAAM,EAAE;YAChC,OAAO;gBAAC,EAAE;aAAC,CAAC;SACb,MAAM,IAAI,KAAK,0KAAK,cAAW,CAAC,KAAK,EAAE;YACtC,OAAO;gBAAC,EAAE;aAAC,CAAC;SACb;QACD,OAAO;YAAC,EAAE;SAAC,CAAC;IACd,CAAC;IAEO,sBAAsB,CAC5B,4KAAyB,mBAAgB,CAAC,OAAO,EAAA;QAEjD,IAAI,IAAI,0KAAK,mBAAgB,CAAC,OAAO,EAAE;YACrC,OAAO;gBAAC,EAAE;gBAAE,EAAE;gBAAE,EAAE;gBAAE,EAAE;gBAAE,EAAE;aAAC,CAAC;SAC7B,MAAM,IAAI,IAAI,0KAAK,mBAAgB,CAAC,KAAK,EAAE;YAC1C,OAAO;gBAAC,EAAE;gBAAE,EAAE;gBAAE,EAAE;aAAC,CAAC;SACrB;QACD,OAAO;YAAC,EAAE;YAAE,EAAE;YAAE,EAAE;SAAC,CAAC;IACtB,CAAC;IAEO,kBAAkB,CAAC,OAAiB,EAAA;QAC1C,IAAI,SAAS,CAAC,OAAO,EAAE;YACrB,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;SAC5B,MAAM;YACL,MAAM,IAAI,CAAC,WAAW,CAAC,0CAA0C,CAAC,CAAC;SACpE;IACH,CAAC;CACF", "ignoreList": [0], "debugId": null}}]}