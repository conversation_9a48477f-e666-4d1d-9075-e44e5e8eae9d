{"version": 3, "file": "web.js", "sourceRoot": "", "sources": ["../../src/web.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAQ5C,MAAM,OAAO,eAAgB,SAAQ,SAAS;IAC5C,KAAK,CAAC,IAAI,CAAC,QAAsB;QAC/B,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,QAAsB;QAC/B,OAAO,SAAS,CAAC;IACnB,CAAC;CACF", "sourcesContent": ["import { WebPlugin } from '@capacitor/core';\n\nimport type {\n  HideOptions,\n  ShowOptions,\n  SplashScreenPlugin,\n} from './definitions';\n\nexport class SplashScreenWeb extends WebPlugin implements SplashScreenPlugin {\n  async show(_options?: ShowOptions): Promise<void> {\n    return undefined;\n  }\n\n  async hide(_options?: HideOptions): Promise<void> {\n    return undefined;\n  }\n}\n"]}