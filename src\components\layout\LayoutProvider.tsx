'use client'

import { useEffect, useState } from 'react'
import { isMobile } from '@/lib/capacitor'
import MobileLayout from '@/components/mobile/MobileLayout'
import Sidebar from '@/components/layout/Sidebar'

interface LayoutProviderProps {
  children: React.ReactNode
}

export default function LayoutProvider({ children }: LayoutProviderProps) {
  const [isMobileDevice, setIsMobileDevice] = useState(false)
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setIsMobileDevice(isMobile())
    setMounted(true)
  }, [])

  // Prevent hydration mismatch by not rendering until mounted
  if (!mounted) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  // Use mobile layout for mobile devices
  if (isMobileDevice) {
    return <MobileLayout>{children}</MobileLayout>
  }

  // Use desktop layout for larger screens
  return (
    <div className="flex h-screen bg-gray-100">
      <Sidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50">
          <div className="container mx-auto px-6 py-8">
            {children}
          </div>
        </main>
      </div>
    </div>
  )
}
