{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,cAAc,EAAE,MAAM,iBAAiB,CAAC;AAIjD,MAAM,OAAO,GAAG,cAAc,CAAgB,SAAS,EAAE;IACvD,GAAG,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,CAAC;CACzD,CAAC,CAAC;AAEH,cAAc,eAAe,CAAC;AAC9B,OAAO,EAAE,OAAO,EAAE,CAAC", "sourcesContent": ["import { registerPlugin } from '@capacitor/core';\n\nimport type { HapticsPlugin } from './definitions';\n\nconst Haptics = registerPlugin<HapticsPlugin>('Haptics', {\n  web: () => import('./web').then(m => new m.HapticsWeb()),\n});\n\nexport * from './definitions';\nexport { Haptics };\n"]}