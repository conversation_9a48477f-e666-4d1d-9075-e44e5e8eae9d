{"version": 3, "file": "definitions.js", "sourceRoot": "", "sources": ["../../src/definitions.ts"], "names": [], "mappings": "AAAA,wCAAwC;AAoDxC,MAAM,CAAN,IAAY,KAuBX;AAvBD,WAAY,KAAK;IACf;;;;OAIG;IACH,sBAAa,CAAA;IAEb;;;;OAIG;IACH,wBAAe,CAAA;IAEf;;;;;;OAMG;IACH,4BAAmB,CAAA;AACrB,CAAC,EAvBW,KAAK,KAAL,KAAK,QAuBhB;AAeD,MAAM,CAAN,IAAY,SAwBX;AAxBD,WAAY,SAAS;IACnB;;;;OAIG;IACH,0BAAa,CAAA;IAEb;;;;;;;OAOG;IACH,4BAAe,CAAA;IAEf;;;;OAIG;IACH,0BAAa,CAAA;AACf,CAAC,EAxBW,SAAS,KAAT,SAAS,QAwBpB;AAiID;;;GAGG;AACH,MAAM,CAAC,MAAM,kBAAkB,GAAG,SAAS,CAAC;AAE5C;;;GAGG;AACH,MAAM,CAAC,MAAM,cAAc,GAAG,KAAK,CAAC", "sourcesContent": ["/// <reference types=\"@capacitor/cli\" />\n\ndeclare module '@capacitor/cli' {\n  export interface PluginsConfig {\n    /**\n     * These config values are available:\n     */\n    StatusBar?: {\n      /**\n       * Whether the statusbar is overlaid or not.\n       * For applications targeting Android 15, this property has no effect unless\n       * the property windowOptOutEdgeToEdgeEnforcement is added to the application layout file.\n       * Otherwise, the application assumes always overlays as true.\n       * More details in https://developer.android.com/reference/android/R.attr#windowOptOutEdgeToEdgeEnforcement\n       *\n       * @since 1.0.0\n       * @default true\n       * @example false\n       */\n      overlaysWebView?: boolean;\n\n      /**\n       * Style of the text of the status bar.\n       *\n       * @since 1.0.0\n       * @default default\n       * @example \"DARK\"\n       */\n      style?: string;\n\n      /**\n       * Color of the background of the statusbar in hex format, #RRGGBB.\n       * Doesn't work if `overlaysWebView` is true.\n       *\n       * @since 1.0.0\n       * @default #000000\n       * @example \"#ffffffff\"\n       */\n      backgroundColor?: string;\n    };\n  }\n}\n\nexport interface StyleOptions {\n  /**\n   * Style of the text of the status bar.\n   *\n   * @since 1.0.0\n   */\n  style: Style;\n}\n\nexport enum Style {\n  /**\n   * Light text for dark backgrounds.\n   *\n   * @since 1.0.0\n   */\n  Dark = 'DARK',\n\n  /**\n   * Dark text for light backgrounds.\n   *\n   * @since 1.0.0\n   */\n  Light = 'LIGHT',\n\n  /**\n   * The style is based on the device appearance.\n   * If the device is using Dark mode, the statusbar text will be light.\n   * If the device is using Light mode, the statusbar text will be dark.\n   *\n   * @since 1.0.0\n   */\n  Default = 'DEFAULT',\n}\n\nexport interface AnimationOptions {\n  /**\n   * The type of status bar animation used when showing or hiding.\n   *\n   * This option is only supported on iOS.\n   *\n   * @default Animation.Fade\n   *\n   * @since 1.0.0\n   */\n  animation: Animation;\n}\n\nexport enum Animation {\n  /**\n   * No animation during show/hide.\n   *\n   * @since 1.0.0\n   */\n  None = 'NONE',\n\n  /**\n   * Slide animation during show/hide.\n   * It doesn't work on iOS 15+.\n   *\n   * @deprecated Use Animation.Fade or Animation.None instead.\n   *\n   * @since 1.0.0\n   */\n  Slide = 'SLIDE',\n\n  /**\n   * Fade animation during show/hide.\n   *\n   * @since 1.0.0\n   */\n  Fade = 'FADE',\n}\n\nexport interface BackgroundColorOptions {\n  /**\n   * A hex color to which the status bar color is set.\n   *\n   * @since 1.0.0\n   */\n  color: string;\n}\n\nexport interface StatusBarInfo {\n  /**\n   * Whether the status bar is visible or not.\n   *\n   * @since 1.0.0\n   */\n  visible: boolean;\n\n  /**\n   * The current status bar style.\n   *\n   * @since 1.0.0\n   */\n  style: Style;\n\n  /**\n   * The current status bar color.\n   *\n   * @since 1.0.0\n   */\n  color?: string;\n\n  /**\n   * Whether the statusbar is overlaid or not.\n   *\n   * @since 1.0.0\n   */\n  overlays?: boolean;\n}\n\nexport interface SetOverlaysWebViewOptions {\n  /**\n   * Whether to overlay the status bar or not.\n   *\n   * @since 1.0.0\n   */\n  overlay: boolean;\n}\n\nexport interface StatusBarPlugin {\n  /**\n   * Set the current style of the status bar.\n   *\n   * @since 1.0.0\n   */\n  setStyle(options: StyleOptions): Promise<void>;\n\n  /**\n   * Set the background color of the status bar.\n   *\n   * @since 1.0.0\n   */\n  setBackgroundColor(options: BackgroundColorOptions): Promise<void>;\n\n  /**\n   * Show the status bar.\n   * On iOS, if the status bar is initially hidden and the initial style is set to\n   * `UIStatusBarStyleLightContent`, first show call might present a glitch on the\n   * animation showing the text as dark and then transition to light. It's recommended\n   * to use `Animation.None` as the animation on the first call.\n   *\n   * @since 1.0.0\n   */\n  show(options?: AnimationOptions): Promise<void>;\n\n  /**\n   * Hide the status bar.\n   *\n   * @since 1.0.0\n   */\n  hide(options?: AnimationOptions): Promise<void>;\n\n  /**\n   * Get info about the current state of the status bar.\n   *\n   * @since 1.0.0\n   */\n  getInfo(): Promise<StatusBarInfo>;\n\n  /**\n   * Set whether or not the status bar should overlay the webview to allow usage\n   * of the space underneath it.\n   *\n   * @since 1.0.0\n   */\n  setOverlaysWebView(options: SetOverlaysWebViewOptions): Promise<void>;\n}\n\n/**\n * @deprecated Use `StyleOptions`.\n * @since 1.0.0\n */\nexport type StatusBarStyleOptions = StyleOptions;\n\n/**\n * @deprecated Use `BackgroundColorOptions`.\n * @since 1.0.0\n */\nexport type StatusBarBackgroundColorOptions = BackgroundColorOptions;\n\n/**\n * @deprecated Use `SetOverlaysWebViewOptions`.\n * @since 1.0.0\n */\nexport type StatusBarOverlaysWebviewOptions = SetOverlaysWebViewOptions;\n\n/**\n * @deprecated Use `StatusBarInfo`.\n * @since 1.0.0\n */\nexport type StatusBarInfoResult = StatusBarInfo;\n\n/**\n * @deprecated Use `AnimationOptions`.\n * @since 1.0.0\n */\nexport type StatusBarAnimationOptions = AnimationOptions;\n\n/**\n * @deprecated Use `Animation`.\n * @since 1.0.0\n */\nexport const StatusBarAnimation = Animation;\n\n/**\n * @deprecated Use `Style`.\n * @since 1.0.0\n */\nexport const StatusBarStyle = Style;\n"]}