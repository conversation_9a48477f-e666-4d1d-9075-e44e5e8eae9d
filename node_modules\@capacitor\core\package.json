{"name": "@capacitor/core", "version": "7.4.1", "description": "Capacitor: Cross-platform apps with JavaScript and the web", "homepage": "https://capacitorjs.com", "author": "Ionic Team <<EMAIL>> (https://ionic.io)", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/ionic-team/capacitor.git"}, "bugs": {"url": "https://github.com/ionic-team/capacitor/issues"}, "files": ["dist/", "types/", "cookies.md", "cordova.js", "http.md"], "main": "dist/index.cjs.js", "module": "dist/index.js", "types": "types/index.d.ts", "unpkg": "dist/capacitor.js", "scripts": {"build": "npm run clean && npm run docgen && npm run transpile && npm run rollup", "build:nativebridge": "tsc native-bridge.ts --target es2017 --moduleResolution node --outDir build && rollup --config rollup.bridge.config.js", "clean": "<PERSON><PERSON><PERSON> dist", "docgen": "docgen --api CapacitorCookiesPlugin --output-readme cookies.md && docgen --api CapacitorHttpPlugin --output-readme http.md", "prepublishOnly": "npm run build", "rollup": "rollup --config rollup.config.js", "transpile": "tsc", "test": "jest", "test.watch": "jest --watchAll", "test.treeshaking": "node src/tests/build-treeshaking.js"}, "dependencies": {"tslib": "^2.1.0"}, "devDependencies": {"@capacitor/docgen": "^0.2.2", "@rollup/plugin-node-resolve": "^10.0.0", "@rollup/plugin-replace": "^2.4.2", "@types/jest": "^29.5.0", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "jest-jasmine2": "^29.5.0", "rimraf": "^4.4.1", "rollup": "^2.21.0", "rollup-plugin-terser": "^7.0.2", "typescript": "~5.0.2"}, "jest": {"preset": "ts-jest", "testRunner": "jest-jasmine2"}, "publishConfig": {"access": "public"}}