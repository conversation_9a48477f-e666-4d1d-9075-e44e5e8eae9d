/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable static export for Capacitor
  output: process.env.CAPACITOR_BUILD === 'true' ? 'export' : undefined,
  
  // Disable image optimization for static export
  images: {
    unoptimized: process.env.CAPACITOR_BUILD === 'true'
  },
  
  // Configure trailing slash for static export
  trailingSlash: process.env.CAPACITOR_BUILD === 'true',
  
  // Disable server-side features for static export
  experimental: {
    // esmExternals removed as it's not supported with Turbopack
  },
  
  // Configure asset prefix for Capacitor
  assetPrefix: process.env.CAPACITOR_BUILD === 'true' ? './' : undefined,
  
  // Webpack configuration
  webpack: (config, { isServer }) => {
    // Handle file imports
    config.module.rules.push({
      test: /\.(png|jpe?g|gif|svg)$/,
      use: {
        loader: 'file-loader',
        options: {
          publicPath: '/_next/static/images/',
          outputPath: 'static/images/',
          esModule: false
        }
      }
    })
    
    return config
  },
  
  // Environment variables for build detection
  env: {
    CAPACITOR_BUILD: process.env.CAPACITOR_BUILD || 'false'
  }
}

module.exports = nextConfig
