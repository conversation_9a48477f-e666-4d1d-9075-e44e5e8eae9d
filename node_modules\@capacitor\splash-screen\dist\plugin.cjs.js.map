{"version": 3, "file": "plugin.cjs.js", "sources": ["esm/index.js", "esm/web.js"], "sourcesContent": ["import { registerPlugin } from '@capacitor/core';\nconst SplashScreen = registerPlugin('SplashScreen', {\n    web: () => import('./web').then(m => new m.SplashScreenWeb()),\n});\nexport * from './definitions';\nexport { SplashScreen };\n//# sourceMappingURL=index.js.map", "import { WebPlugin } from '@capacitor/core';\nexport class SplashScreenWeb extends WebPlugin {\n    async show(_options) {\n        return undefined;\n    }\n    async hide(_options) {\n        return undefined;\n    }\n}\n//# sourceMappingURL=web.js.map"], "names": ["registerPlugin", "WebPlugin"], "mappings": ";;;;AACK,MAAC,YAAY,GAAGA,mBAAc,CAAC,cAAc,EAAE;AACpD,IAAI,GAAG,EAAE,MAAM,mDAAe,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,eAAe,EAAE,CAAC;AACjE,CAAC;;ACFM,MAAM,eAAe,SAASC,cAAS,CAAC;AAC/C,IAAI,MAAM,IAAI,CAAC,QAAQ,EAAE;AACzB,QAAQ,OAAO,SAAS;AACxB;AACA,IAAI,MAAM,IAAI,CAAC,QAAQ,EAAE;AACzB,QAAQ,OAAO,SAAS;AACxB;AACA;;;;;;;;;"}