'use client'

import { useState, useEffect, useRef } from 'react'
import { createClient } from '@/lib/supabase/client'
import { SendIcon, MessageCircleIcon, PlusIcon, BotIcon, UserIcon, TrashIcon, EditIcon, SparklesIcon, TrendingUpIcon, CheckCircleIcon, ShoppingCartIcon, DollarSignIcon } from 'lucide-react'
import { Database } from '@/lib/types/database'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Badge'
import { Input } from '@/components/ui/Input'
import { Loading } from '@/components/ui/Loading'
import { motion, AnimatePresence } from 'framer-motion'
import toast from 'react-hot-toast'

type ChatConversation = Database['public']['Tables']['chat_conversations']['Row']
type ChatMessage = Database['public']['Tables']['chat_messages']['Row']

export default function ChatPage() {
  const [conversations, setConversations] = useState<ChatConversation[]>([])
  const [currentConversation, setCurrentConversation] = useState<string | null>(null)
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [newMessage, setNewMessage] = useState('')
  const [loading, setLoading] = useState(true)
  const [sending, setSending] = useState(false)
  const [editingConversation, setEditingConversation] = useState<string | null>(null)
  const [editingTitle, setEditingTitle] = useState('')
  const [showSuggestions, setShowSuggestions] = useState(true)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const supabase = createClient()

  const quickSuggestions = [
    { icon: CheckCircleIcon, text: "What tasks should I prioritize today?", color: "text-blue-600" },
    { icon: DollarSignIcon, text: "How is my budget looking this month?", color: "text-green-600" },
    { icon: ShoppingCartIcon, text: "Help me create a grocery list", color: "text-purple-600" },
    { icon: TrendingUpIcon, text: "Give me insights on my spending", color: "text-orange-600" },
  ]

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    fetchConversations()

    // Set up real-time subscriptions
    const messagesSubscription = supabase
      .channel('chat_messages_changes')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'chat_messages' },
        (payload) => {
          console.log('Chat message change received:', payload)
          if (currentConversation && payload.new && (payload.new as any).conversation_id === currentConversation) {
            fetchMessages(currentConversation)
          }
        }
      )
      .subscribe()

    return () => {
      supabase.removeChannel(messagesSubscription)
    }
  }, [])

  useEffect(() => {
    if (currentConversation) {
      fetchMessages(currentConversation)
      setShowSuggestions(false)
    }
  }, [currentConversation])

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const fetchConversations = async () => {
    try {
      const { data, error } = await supabase
        .from('chat_conversations')
        .select('*')
        .order('updated_at', { ascending: false })

      if (error) throw error
      setConversations(data || [])
      
      if (data && data.length > 0 && !currentConversation) {
        setCurrentConversation(data[0].id)
      }
    } catch (error) {
      console.error('Error fetching conversations:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchMessages = async (conversationId: string) => {
    try {
      const { data, error } = await supabase
        .from('chat_messages')
        .select('*')
        .eq('conversation_id', conversationId)
        .order('created_at', { ascending: true })

      if (error) throw error
      setMessages(data || [])
    } catch (error) {
      console.error('Error fetching messages:', error)
    }
  }

  const createNewConversation = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) return

      const { data, error } = await supabase
        .from('chat_conversations')
        .insert([
          {
            user_id: user.id,
            title: 'New Conversation',
          },
        ])
        .select()
        .single()

      if (error) throw error

      setConversations([data, ...conversations])
      setCurrentConversation(data.id)
      setMessages([])
      setShowSuggestions(true)
      toast.success('New conversation created!')
    } catch (error) {
      console.error('Error creating conversation:', error)
      toast.error('Failed to create conversation. Please try again.')
    }
  }

  const deleteConversation = async (conversationId: string) => {
    if (!confirm('Are you sure you want to delete this conversation?')) return

    try {
      const { error } = await supabase
        .from('chat_conversations')
        .delete()
        .eq('id', conversationId)

      if (error) throw error

      setConversations(conversations.filter(c => c.id !== conversationId))
      if (currentConversation === conversationId) {
        setCurrentConversation(null)
        setMessages([])
        setShowSuggestions(true)
      }
      toast.success('Conversation deleted successfully!')
    } catch (error) {
      console.error('Error deleting conversation:', error)
      toast.error('Failed to delete conversation. Please try again.')
    }
  }

  const updateConversationTitle = async (conversationId: string, newTitle: string) => {
    try {
      const { error } = await supabase
        .from('chat_conversations')
        .update({ title: newTitle })
        .eq('id', conversationId)

      if (error) throw error

      setConversations(conversations.map(c =>
        c.id === conversationId ? { ...c, title: newTitle } : c
      ))
      setEditingConversation(null)
      setEditingTitle('')
      toast.success('Conversation title updated!')
    } catch (error) {
      console.error('Error updating conversation title:', error)
      toast.error('Failed to update title. Please try again.')
    }
  }

  const handleSuggestionClick = (suggestion: string) => {
    setNewMessage(suggestion)
    setShowSuggestions(false)
  }

  const sendMessage = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!newMessage.trim() || !currentConversation || sending) return

    setSending(true)
    const messageText = newMessage.trim()
    setNewMessage('')
    setShowSuggestions(false)

    try {
      // Add user message to UI immediately
      const userMessage: ChatMessage = {
        id: 'temp-' + Date.now(),
        conversation_id: currentConversation,
        role: 'user',
        content: messageText,
        created_at: new Date().toISOString(),
      }
      setMessages(prev => [...prev, userMessage])

      // Add typing indicator
      const typingMessage: ChatMessage = {
        id: 'typing-' + Date.now(),
        conversation_id: currentConversation,
        role: 'assistant',
        content: '...',
        created_at: new Date().toISOString(),
      }
      setMessages(prev => [...prev, typingMessage])

      // Send to API
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: messageText,
          conversationId: currentConversation,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to send message')
      }

      const { response: aiResponse } = await response.json()

      // Remove typing indicator and add AI response
      setMessages(prev => prev.filter(msg => !msg.id.startsWith('typing-')))

      const aiMessage: ChatMessage = {
        id: 'temp-ai-' + Date.now(),
        conversation_id: currentConversation,
        role: 'assistant',
        content: aiResponse,
        created_at: new Date().toISOString(),
      }
      setMessages(prev => [...prev, aiMessage])

      // Update conversation title if it's the first message
      if (messages.length === 0) {
        const title = messageText.length > 30 ? messageText.substring(0, 30) + '...' : messageText
        updateConversationTitle(currentConversation, title)
      }

      // Refresh messages from database to get real IDs
      setTimeout(() => fetchMessages(currentConversation), 500)
    } catch (error) {
      console.error('Error sending message:', error)
      // Remove typing indicator on error
      setMessages(prev => prev.filter(msg => !msg.id.startsWith('typing-')))
      toast.error('Failed to send message. Please try again.')
    } finally {
      setSending(false)
    }
  }

  if (loading) {
    return <Loading size="lg" text="Loading AI Chat..." className="h-64" />
  }

  return (
    <div className="h-[calc(100vh-4rem)] flex bg-gray-50 -mx-4 -my-8 sm:-mx-6 lg:-mx-8">
      {/* Conversations Sidebar */}
      <div className="w-1/4 bg-white border-r border-gray-200 flex flex-col shadow-sm">
        <div className="p-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
          <div className="flex items-center justify-between mb-3">
            <h2 className="text-lg font-semibold text-gray-900 flex items-center">
              <SparklesIcon className="h-5 w-5 mr-2 text-blue-600" />
              AI Chat
            </h2>
          </div>
          <Button
            onClick={createNewConversation}
            className="w-full"
            size="sm"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            New Chat
          </Button>
        </div>
        <div className="flex-1 overflow-y-auto">
          {conversations.length === 0 ? (
            <div className="p-6 text-center text-gray-500">
              <MessageCircleIcon className="mx-auto h-12 w-12 mb-3 text-gray-300" />
              <p className="text-sm font-medium">No conversations yet</p>
              <p className="text-xs text-gray-400 mt-1">Start a new chat to get help with your life management</p>
            </div>
          ) : (
            <div className="space-y-1 p-3">
              {conversations.map((conversation) => (
                <motion.div
                  key={conversation.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  className="group relative"
                >
                  <button
                    onClick={() => setCurrentConversation(conversation.id)}
                    className={`w-full text-left p-3 rounded-lg transition-all duration-200 ${
                      currentConversation === conversation.id
                        ? 'bg-blue-100 text-blue-900 shadow-sm'
                        : 'hover:bg-gray-50'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        {editingConversation === conversation.id ? (
                          <input
                            type="text"
                            value={editingTitle}
                            onChange={(e) => setEditingTitle(e.target.value)}
                            onBlur={() => updateConversationTitle(conversation.id, editingTitle)}
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') {
                                updateConversationTitle(conversation.id, editingTitle)
                              } else if (e.key === 'Escape') {
                                setEditingConversation(null)
                                setEditingTitle('')
                              }
                            }}
                            className="w-full text-sm font-medium bg-transparent border-none outline-none"
                            autoFocus
                          />
                        ) : (
                          <div className="font-medium text-sm truncate">
                            {conversation.title}
                          </div>
                        )}
                        <div className="text-xs text-gray-500 mt-1">
                          {new Date(conversation.updated_at).toLocaleDateString()}
                        </div>
                      </div>
                      <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            setEditingConversation(conversation.id)
                            setEditingTitle(conversation.title)
                          }}
                          className="p-1 hover:bg-gray-200 rounded"
                        >
                          <EditIcon className="h-3 w-3 text-gray-500" />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            deleteConversation(conversation.id)
                          }}
                          className="p-1 hover:bg-red-100 rounded"
                        >
                          <TrashIcon className="h-3 w-3 text-red-500" />
                        </button>
                      </div>
                    </div>
                  </button>
                </motion.div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Chat Area */}
      <div className="flex-1 flex flex-col bg-white">
        {currentConversation ? (
          <>
            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-6 space-y-6">
              {messages.length === 0 && showSuggestions ? (
                <div className="text-center space-y-8">
                  <div className="text-gray-500">
                    <BotIcon className="mx-auto h-16 w-16 mb-4 text-blue-500" />
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">Welcome to AI Chat!</h3>
                    <p className="text-gray-600 max-w-md mx-auto">
                      I'm your personal AI assistant for LifeManager. I can help you with tasks, budgets, shopping lists, and recipes.
                    </p>
                  </div>

                  {/* Quick Suggestions */}
                  <div className="max-w-2xl mx-auto">
                    <h4 className="text-sm font-medium text-gray-700 mb-4">Quick suggestions to get started:</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {quickSuggestions.map((suggestion, index) => (
                        <motion.button
                          key={index}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: index * 0.1 }}
                          onClick={() => handleSuggestionClick(suggestion.text)}
                          className="p-4 text-left bg-gray-50 hover:bg-gray-100 rounded-lg border border-gray-200 transition-colors group"
                        >
                          <div className="flex items-center space-x-3">
                            <suggestion.icon className={`h-5 w-5 ${suggestion.color}`} />
                            <span className="text-sm text-gray-700 group-hover:text-gray-900">
                              {suggestion.text}
                            </span>
                          </div>
                        </motion.button>
                      ))}
                    </div>
                  </div>
                </div>
              ) : (
                <AnimatePresence>
                  {messages.map((message, index) => (
                    <motion.div
                      key={message.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3 }}
                      className={`flex ${
                        message.role === 'user' ? 'justify-end' : 'justify-start'
                      }`}
                    >
                      <div className={`flex items-start space-x-3 max-w-3xl ${
                        message.role === 'user' ? 'flex-row-reverse space-x-reverse' : ''
                      }`}>
                        {/* Avatar */}
                        <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                          message.role === 'user'
                            ? 'bg-blue-600'
                            : 'bg-gradient-to-br from-purple-500 to-blue-600'
                        }`}>
                          {message.role === 'user' ? (
                            <UserIcon className="h-4 w-4 text-white" />
                          ) : (
                            <BotIcon className="h-4 w-4 text-white" />
                          )}
                        </div>

                        {/* Message Content */}
                        <div className={`flex-1 ${
                          message.role === 'user' ? 'text-right' : 'text-left'
                        }`}>
                          <div className={`inline-block px-4 py-3 rounded-2xl shadow-sm ${
                            message.role === 'user'
                              ? 'bg-blue-600 text-white'
                              : message.content === '...'
                                ? 'bg-gray-100 text-gray-500'
                                : 'bg-gray-100 text-gray-900'
                          }`}>
                            {message.content === '...' ? (
                              <div className="flex space-x-1">
                                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                              </div>
                            ) : (
                              <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                            )}
                          </div>
                          <div className={`text-xs text-gray-500 mt-1 ${
                            message.role === 'user' ? 'text-right' : 'text-left'
                          }`}>
                            {new Date(message.created_at).toLocaleTimeString([], {
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </AnimatePresence>
              )}
              <div ref={messagesEndRef} />
            </div>

            {/* Message Input */}
            <div className="border-t border-gray-200 p-4">
              <form onSubmit={sendMessage} className="flex space-x-2">
                <input
                  type="text"
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  placeholder="Type your message..."
                  className="flex-1 border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  disabled={sending}
                />
                <button
                  type="submit"
                  disabled={!newMessage.trim() || sending}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <SendIcon className="h-4 w-4" />
                </button>
              </form>
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center text-gray-500">
              <MessageCircleIcon className="mx-auto h-12 w-12 mb-4" />
              <h3 className="text-lg font-medium">Select a conversation</h3>
              <p className="text-sm">Choose a conversation from the sidebar or start a new one</p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
