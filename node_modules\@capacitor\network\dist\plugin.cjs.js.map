{"version": 3, "file": "plugin.cjs.js", "sources": ["esm/index.js", "esm/web.js"], "sourcesContent": ["import { registerPlugin } from '@capacitor/core';\nconst Network = registerPlugin('Network', {\n    web: () => import('./web').then(m => new m.NetworkWeb()),\n});\nexport * from './definitions';\nexport { Network };\n//# sourceMappingURL=index.js.map", "import { WebPlugin } from '@capacitor/core';\nfunction translatedConnection() {\n    const connection = window.navigator.connection ||\n        window.navigator.mozConnection ||\n        window.navigator.webkitConnection;\n    let result = 'unknown';\n    const type = connection ? connection.type || connection.effectiveType : null;\n    if (type && typeof type === 'string') {\n        switch (type) {\n            // possible type values\n            case 'bluetooth':\n            case 'cellular':\n                result = 'cellular';\n                break;\n            case 'none':\n                result = 'none';\n                break;\n            case 'ethernet':\n            case 'wifi':\n            case 'wimax':\n                result = 'wifi';\n                break;\n            case 'other':\n            case 'unknown':\n                result = 'unknown';\n                break;\n            // possible effectiveType values\n            case 'slow-2g':\n            case '2g':\n            case '3g':\n                result = 'cellular';\n                break;\n            case '4g':\n                result = 'wifi';\n                break;\n            default:\n                break;\n        }\n    }\n    return result;\n}\nexport class NetworkWeb extends WebPlugin {\n    constructor() {\n        super();\n        this.handleOnline = () => {\n            const connectionType = translatedConnection();\n            const status = {\n                connected: true,\n                connectionType: connectionType,\n            };\n            this.notifyListeners('networkStatusChange', status);\n        };\n        this.handleOffline = () => {\n            const status = {\n                connected: false,\n                connectionType: 'none',\n            };\n            this.notifyListeners('networkStatusChange', status);\n        };\n        if (typeof window !== 'undefined') {\n            window.addEventListener('online', this.handleOnline);\n            window.addEventListener('offline', this.handleOffline);\n        }\n    }\n    async getStatus() {\n        if (!window.navigator) {\n            throw this.unavailable('Browser does not support the Network Information API');\n        }\n        const connected = window.navigator.onLine;\n        const connectionType = translatedConnection();\n        const status = {\n            connected,\n            connectionType: connected ? connectionType : 'none',\n        };\n        return status;\n    }\n}\nconst Network = new NetworkWeb();\nexport { Network };\n//# sourceMappingURL=web.js.map"], "names": ["Network", "registerPlugin", "WebPlugin"], "mappings": ";;;;AACK,MAACA,SAAO,GAAGC,mBAAc,CAAC,SAAS,EAAE;AAC1C,IAAI,GAAG,EAAE,MAAM,mDAAe,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,UAAU,EAAE,CAAC;AAC5D,CAAC;;ACFD,SAAS,oBAAoB,GAAG;AAChC,IAAI,MAAM,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC,UAAU;AAClD,QAAQ,MAAM,CAAC,SAAS,CAAC,aAAa;AACtC,QAAQ,MAAM,CAAC,SAAS,CAAC,gBAAgB;AACzC,IAAI,IAAI,MAAM,GAAG,SAAS;AAC1B,IAAI,MAAM,IAAI,GAAG,UAAU,GAAG,UAAU,CAAC,IAAI,IAAI,UAAU,CAAC,aAAa,GAAG,IAAI;AAChF,IAAI,IAAI,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AAC1C,QAAQ,QAAQ,IAAI;AACpB;AACA,YAAY,KAAK,WAAW;AAC5B,YAAY,KAAK,UAAU;AAC3B,gBAAgB,MAAM,GAAG,UAAU;AACnC,gBAAgB;AAChB,YAAY,KAAK,MAAM;AACvB,gBAAgB,MAAM,GAAG,MAAM;AAC/B,gBAAgB;AAChB,YAAY,KAAK,UAAU;AAC3B,YAAY,KAAK,MAAM;AACvB,YAAY,KAAK,OAAO;AACxB,gBAAgB,MAAM,GAAG,MAAM;AAC/B,gBAAgB;AAChB,YAAY,KAAK,OAAO;AACxB,YAAY,KAAK,SAAS;AAC1B,gBAAgB,MAAM,GAAG,SAAS;AAClC,gBAAgB;AAChB;AACA,YAAY,KAAK,SAAS;AAC1B,YAAY,KAAK,IAAI;AACrB,YAAY,KAAK,IAAI;AACrB,gBAAgB,MAAM,GAAG,UAAU;AACnC,gBAAgB;AAChB,YAAY,KAAK,IAAI;AACrB,gBAAgB,MAAM,GAAG,MAAM;AAC/B,gBAAgB;AAGhB;AACA;AACA,IAAI,OAAO,MAAM;AACjB;AACO,MAAM,UAAU,SAASC,cAAS,CAAC;AAC1C,IAAI,WAAW,GAAG;AAClB,QAAQ,KAAK,EAAE;AACf,QAAQ,IAAI,CAAC,YAAY,GAAG,MAAM;AAClC,YAAY,MAAM,cAAc,GAAG,oBAAoB,EAAE;AACzD,YAAY,MAAM,MAAM,GAAG;AAC3B,gBAAgB,SAAS,EAAE,IAAI;AAC/B,gBAAgB,cAAc,EAAE,cAAc;AAC9C,aAAa;AACb,YAAY,IAAI,CAAC,eAAe,CAAC,qBAAqB,EAAE,MAAM,CAAC;AAC/D,SAAS;AACT,QAAQ,IAAI,CAAC,aAAa,GAAG,MAAM;AACnC,YAAY,MAAM,MAAM,GAAG;AAC3B,gBAAgB,SAAS,EAAE,KAAK;AAChC,gBAAgB,cAAc,EAAE,MAAM;AACtC,aAAa;AACb,YAAY,IAAI,CAAC,eAAe,CAAC,qBAAqB,EAAE,MAAM,CAAC;AAC/D,SAAS;AACT,QAAQ,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAC3C,YAAY,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC;AAChE,YAAY,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC;AAClE;AACA;AACA,IAAI,MAAM,SAAS,GAAG;AACtB,QAAQ,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;AAC/B,YAAY,MAAM,IAAI,CAAC,WAAW,CAAC,sDAAsD,CAAC;AAC1F;AACA,QAAQ,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,MAAM;AACjD,QAAQ,MAAM,cAAc,GAAG,oBAAoB,EAAE;AACrD,QAAQ,MAAM,MAAM,GAAG;AACvB,YAAY,SAAS;AACrB,YAAY,cAAc,EAAE,SAAS,GAAG,cAAc,GAAG,MAAM;AAC/D,SAAS;AACT,QAAQ,OAAO,MAAM;AACrB;AACA;AACA,MAAM,OAAO,GAAG,IAAI,UAAU,EAAE;;;;;;;;;;"}