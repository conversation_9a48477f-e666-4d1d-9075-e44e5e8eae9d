module.exports = {

"[project]/node_modules/@capacitor/network/dist/esm/web.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Network": (()=>Network),
    "NetworkWeb": (()=>NetworkWeb)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$capacitor$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@capacitor/core/dist/index.js [app-ssr] (ecmascript)");
;
function translatedConnection() {
    const connection = window.navigator.connection || window.navigator.mozConnection || window.navigator.webkitConnection;
    let result = 'unknown';
    const type = connection ? connection.type || connection.effectiveType : null;
    if (type && typeof type === 'string') {
        switch(type){
            // possible type values
            case 'bluetooth':
            case 'cellular':
                result = 'cellular';
                break;
            case 'none':
                result = 'none';
                break;
            case 'ethernet':
            case 'wifi':
            case 'wimax':
                result = 'wifi';
                break;
            case 'other':
            case 'unknown':
                result = 'unknown';
                break;
            // possible effectiveType values
            case 'slow-2g':
            case '2g':
            case '3g':
                result = 'cellular';
                break;
            case '4g':
                result = 'wifi';
                break;
            default:
                break;
        }
    }
    return result;
}
class NetworkWeb extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$capacitor$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WebPlugin"] {
    constructor(){
        super();
        this.handleOnline = ()=>{
            const connectionType = translatedConnection();
            const status = {
                connected: true,
                connectionType: connectionType
            };
            this.notifyListeners('networkStatusChange', status);
        };
        this.handleOffline = ()=>{
            const status = {
                connected: false,
                connectionType: 'none'
            };
            this.notifyListeners('networkStatusChange', status);
        };
        if (typeof window !== 'undefined') {
            window.addEventListener('online', this.handleOnline);
            window.addEventListener('offline', this.handleOffline);
        }
    }
    async getStatus() {
        if (!window.navigator) {
            throw this.unavailable('Browser does not support the Network Information API');
        }
        const connected = window.navigator.onLine;
        const connectionType = translatedConnection();
        const status = {
            connected,
            connectionType: connected ? connectionType : 'none'
        };
        return status;
    }
}
const Network = new NetworkWeb();
;
 //# sourceMappingURL=web.js.map
}}),

};

//# sourceMappingURL=node_modules_%40capacitor_network_dist_esm_web_5d47fcf0.js.map