{"name": "@ionic/cli-framework-output", "version": "2.2.8", "description": "The log/tasks/spinners portion of Ionic CLI Framework", "homepage": "https://ionicframework.com/", "author": "Ionic Team <<EMAIL>> (https://ionicframework.com)", "main": "./dist/index.js", "types": "./dist/index.d.ts", "engines": {"node": ">=16.0.0"}, "files": ["dist/", "LICENSE", "README.md"], "repository": {"type": "git", "url": "https://github.com/ionic-team/ionic-cli.git"}, "bugs": {"url": "https://github.com/ionic-team/ionic-cli/issues"}, "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "lint": "true", "build": "npm run clean && tsc", "watch": "tsc -w --preserveWatchOutput", "test": "jest --maxWorkers=4", "prepublishOnly": "npm run build"}, "license": "MIT", "dependencies": {"@ionic/utils-terminal": "2.3.5", "debug": "^4.0.0", "tslib": "^2.0.1"}, "devDependencies": {"@ionic/utils-stream": "3.1.7", "@types/debug": "^4.1.1", "@types/inquirer": "0.0.43", "@types/jest": "^26.0.10", "@types/node": "~16.0.0", "jest": "^26.4.2", "jest-cli": "^26.0.1", "lint-staged": "^10.0.2", "ts-jest": "~26.3.0", "typescript": "~4.8.0"}}