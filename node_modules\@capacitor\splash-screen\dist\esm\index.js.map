{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,cAAc,EAAE,MAAM,iBAAiB,CAAC;AAIjD,MAAM,YAAY,GAAG,cAAc,CAAqB,cAAc,EAAE;IACtE,GAAG,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,eAAe,EAAE,CAAC;CAC9D,CAAC,CAAC;AAEH,cAAc,eAAe,CAAC;AAC9B,OAAO,EAAE,YAAY,EAAE,CAAC", "sourcesContent": ["import { registerPlugin } from '@capacitor/core';\n\nimport type { SplashScreenPlugin } from './definitions';\n\nconst SplashScreen = registerPlugin<SplashScreenPlugin>('SplashScreen', {\n  web: () => import('./web').then(m => new m.SplashScreenWeb()),\n});\n\nexport * from './definitions';\nexport { SplashScreen };\n"]}