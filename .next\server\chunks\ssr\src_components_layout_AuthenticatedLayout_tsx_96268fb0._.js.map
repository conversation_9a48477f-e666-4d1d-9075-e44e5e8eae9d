{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/layout/AuthenticatedLayout.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { createClient } from '@/lib/supabase/client'\nimport Sidebar from './Sidebar'\n\ninterface AuthenticatedLayoutProps {\n  children: React.ReactNode\n}\n\nexport default function AuthenticatedLayout({ children }: AuthenticatedLayoutProps) {\n  const [loading, setLoading] = useState(true)\n  const [user, setUser] = useState<any>(null)\n  const router = useRouter()\n  const supabase = createClient()\n\n  useEffect(() => {\n    const checkAuth = async () => {\n      try {\n        const { data: { user }, error } = await supabase.auth.getUser()\n        \n        if (error || !user) {\n          router.push('/login')\n          return\n        }\n        \n        setUser(user)\n      } catch (error) {\n        console.error('Auth check error:', error)\n        router.push('/login')\n      } finally {\n        setLoading(false)\n      }\n    }\n\n    checkAuth()\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        if (event === 'SIGNED_OUT' || !session) {\n          router.push('/login')\n        } else if (event === 'SIGNED_IN' && session) {\n          setUser(session.user)\n        }\n      }\n    )\n\n    return () => subscription.unsubscribe()\n  }, [router, supabase.auth])\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"flex flex-col items-center space-y-6 animate-fade-in\">\n          <div className=\"relative\">\n            <div className=\"animate-spin rounded-full h-16 w-16 border-4 border-blue-200\"></div>\n            <div className=\"animate-spin rounded-full h-16 w-16 border-4 border-blue-600 border-t-transparent absolute top-0 left-0\"></div>\n          </div>\n          <div className=\"text-center\">\n            <p className=\"text-gray-700 text-lg font-medium\">Loading your workspace...</p>\n            <p className=\"text-gray-500 text-sm mt-1\">Please wait while we prepare everything</p>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  if (!user) {\n    return null // Will redirect to login\n  }\n\n  return (\n    <div className=\"h-screen flex overflow-hidden bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50\">\n      {/* Skip Navigation Links */}\n      <a\n        href=\"#main-content\"\n        className=\"sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-blue-600 text-white px-4 py-2 rounded-lg z-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"\n      >\n        Skip to main content\n      </a>\n\n      <Sidebar />\n      <div className=\"flex flex-col w-0 flex-1 overflow-hidden\">\n        <main\n          id=\"main-content\"\n          className=\"flex-1 relative overflow-y-auto focus:outline-none transition-all duration-300 md:ml-20 lg:ml-72\"\n          role=\"main\"\n          aria-label=\"Main content\"\n        >\n          <div className=\"py-6 animate-fade-in\">\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 md:px-8\">\n              {children}\n            </div>\n          </div>\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAWe,SAAS,oBAAoB,EAAE,QAAQ,EAA4B;IAChF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACtC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY;YAChB,IAAI;gBACF,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;gBAE7D,IAAI,SAAS,CAAC,MAAM;oBAClB,OAAO,IAAI,CAAC;oBACZ;gBACF;gBAEA,QAAQ;YACV,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,qBAAqB;gBACnC,OAAO,IAAI,CAAC;YACd,SAAU;gBACR,WAAW;YACb;QACF;QAEA;QAEA,0BAA0B;QAC1B,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,SAAS,IAAI,CAAC,iBAAiB,CAChE,OAAO,OAAO;YACZ,IAAI,UAAU,gBAAgB,CAAC,SAAS;gBACtC,OAAO,IAAI,CAAC;YACd,OAAO,IAAI,UAAU,eAAe,SAAS;gBAC3C,QAAQ,QAAQ,IAAI;YACtB;QACF;QAGF,OAAO,IAAM,aAAa,WAAW;IACvC,GAAG;QAAC;QAAQ,SAAS,IAAI;KAAC;IAE1B,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAEjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAoC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;;;;;;;;;;;;IAKpD;IAEA,IAAI,CAAC,MAAM;QACT,OAAO,KAAK,yBAAyB;;IACvC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBACC,MAAK;gBACL,WAAU;0BACX;;;;;;0BAID,8OAAC,uIAAA,CAAA,UAAO;;;;;0BACR,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,IAAG;oBACH,WAAU;oBACV,MAAK;oBACL,cAAW;8BAEX,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}]}