parts {
  device {
    display {
      width 1080
      height 2160
      x 0
      y 0
    }
  }
  portrait {
    background {
      image port_back.webp
    }
    foreground {
      mask round_corners.webp
    }
    onion {
      image port_fore.webp
    }
  }
}
layouts {
  portrait {
    width 1194
    height 2532
    event EV_SW:0:1
    part1 {
      name portrait
      x 0
      y 0
    }
    part2 {
      name device
      x 54
      y 196
    }
  }
}
