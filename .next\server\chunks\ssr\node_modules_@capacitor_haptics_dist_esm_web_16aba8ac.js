module.exports = {

"[project]/node_modules/@capacitor/haptics/dist/esm/web.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "HapticsWeb": (()=>HapticsWeb)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$capacitor$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@capacitor/core/dist/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$capacitor$2f$haptics$2f$dist$2f$esm$2f$definitions$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@capacitor/haptics/dist/esm/definitions.js [app-ssr] (ecmascript)");
;
;
class HapticsWeb extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$capacitor$2f$core$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WebPlugin"] {
    constructor(){
        super(...arguments);
        this.selectionStarted = false;
    }
    async impact(options) {
        const pattern = this.patternForImpact(options === null || options === void 0 ? void 0 : options.style);
        this.vibrateWithPattern(pattern);
    }
    async notification(options) {
        const pattern = this.patternForNotification(options === null || options === void 0 ? void 0 : options.type);
        this.vibrateWithPattern(pattern);
    }
    async vibrate(options) {
        const duration = (options === null || options === void 0 ? void 0 : options.duration) || 300;
        this.vibrateWithPattern([
            duration
        ]);
    }
    async selectionStart() {
        this.selectionStarted = true;
    }
    async selectionChanged() {
        if (this.selectionStarted) {
            this.vibrateWithPattern([
                70
            ]);
        }
    }
    async selectionEnd() {
        this.selectionStarted = false;
    }
    patternForImpact(style = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$capacitor$2f$haptics$2f$dist$2f$esm$2f$definitions$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ImpactStyle"].Heavy) {
        if (style === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$capacitor$2f$haptics$2f$dist$2f$esm$2f$definitions$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ImpactStyle"].Medium) {
            return [
                43
            ];
        } else if (style === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$capacitor$2f$haptics$2f$dist$2f$esm$2f$definitions$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ImpactStyle"].Light) {
            return [
                20
            ];
        }
        return [
            61
        ];
    }
    patternForNotification(type = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$capacitor$2f$haptics$2f$dist$2f$esm$2f$definitions$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NotificationType"].Success) {
        if (type === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$capacitor$2f$haptics$2f$dist$2f$esm$2f$definitions$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NotificationType"].Warning) {
            return [
                30,
                40,
                30,
                50,
                60
            ];
        } else if (type === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$capacitor$2f$haptics$2f$dist$2f$esm$2f$definitions$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NotificationType"].Error) {
            return [
                27,
                45,
                50
            ];
        }
        return [
            35,
            65,
            21
        ];
    }
    vibrateWithPattern(pattern) {
        if (navigator.vibrate) {
            navigator.vibrate(pattern);
        } else {
            throw this.unavailable('Browser does not support the vibrate API');
        }
    }
} //# sourceMappingURL=web.js.map
}}),

};

//# sourceMappingURL=node_modules_%40capacitor_haptics_dist_esm_web_16aba8ac.js.map