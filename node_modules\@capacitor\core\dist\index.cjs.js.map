{"version": 3, "file": "index.cjs.js", "sources": ["../build/util.js", "../build/runtime.js", "../build/global.js", "../build/web-plugin.js", "../build/core-plugins.js"], "sourcesContent": ["export var ExceptionCode;\n(function (ExceptionCode) {\n    /**\n     * API is not implemented.\n     *\n     * This usually means the API can't be used because it is not implemented for\n     * the current platform.\n     */\n    ExceptionCode[\"Unimplemented\"] = \"UNIMPLEMENTED\";\n    /**\n     * API is not available.\n     *\n     * This means the API can't be used right now because:\n     *   - it is currently missing a prerequisite, such as network connectivity\n     *   - it requires a particular platform or browser version\n     */\n    ExceptionCode[\"Unavailable\"] = \"UNAVAILABLE\";\n})(ExceptionCode || (ExceptionCode = {}));\nexport class CapacitorException extends Error {\n    constructor(message, code, data) {\n        super(message);\n        this.message = message;\n        this.code = code;\n        this.data = data;\n    }\n}\nexport const getPlatformId = (win) => {\n    var _a, _b;\n    if (win === null || win === void 0 ? void 0 : win.androidBridge) {\n        return 'android';\n    }\n    else if ((_b = (_a = win === null || win === void 0 ? void 0 : win.webkit) === null || _a === void 0 ? void 0 : _a.messageHandlers) === null || _b === void 0 ? void 0 : _b.bridge) {\n        return 'ios';\n    }\n    else {\n        return 'web';\n    }\n};\n//# sourceMappingURL=util.js.map", "import { CapacitorException, getPlatformId, ExceptionCode } from './util';\nexport const createCapacitor = (win) => {\n    const capCustomPlatform = win.CapacitorCustomPlatform || null;\n    const cap = win.Capacitor || {};\n    const Plugins = (cap.Plugins = cap.Plugins || {});\n    const getPlatform = () => {\n        return capCustomPlatform !== null ? capCustomPlatform.name : getPlatformId(win);\n    };\n    const isNativePlatform = () => getPlatform() !== 'web';\n    const isPluginAvailable = (pluginName) => {\n        const plugin = registeredPlugins.get(pluginName);\n        if (plugin === null || plugin === void 0 ? void 0 : plugin.platforms.has(getPlatform())) {\n            // JS implementation available for the current platform.\n            return true;\n        }\n        if (getPluginHeader(pluginName)) {\n            // Native implementation available.\n            return true;\n        }\n        return false;\n    };\n    const getPluginHeader = (pluginName) => { var _a; return (_a = cap.PluginHeaders) === null || _a === void 0 ? void 0 : _a.find((h) => h.name === pluginName); };\n    const handleError = (err) => win.console.error(err);\n    const registeredPlugins = new Map();\n    const registerPlugin = (pluginName, jsImplementations = {}) => {\n        const registeredPlugin = registeredPlugins.get(pluginName);\n        if (registeredPlugin) {\n            console.warn(`Capacitor plugin \"${pluginName}\" already registered. Cannot register plugins twice.`);\n            return registeredPlugin.proxy;\n        }\n        const platform = getPlatform();\n        const pluginHeader = getPluginHeader(pluginName);\n        let jsImplementation;\n        const loadPluginImplementation = async () => {\n            if (!jsImplementation && platform in jsImplementations) {\n                jsImplementation =\n                    typeof jsImplementations[platform] === 'function'\n                        ? (jsImplementation = await jsImplementations[platform]())\n                        : (jsImplementation = jsImplementations[platform]);\n            }\n            else if (capCustomPlatform !== null && !jsImplementation && 'web' in jsImplementations) {\n                jsImplementation =\n                    typeof jsImplementations['web'] === 'function'\n                        ? (jsImplementation = await jsImplementations['web']())\n                        : (jsImplementation = jsImplementations['web']);\n            }\n            return jsImplementation;\n        };\n        const createPluginMethod = (impl, prop) => {\n            var _a, _b;\n            if (pluginHeader) {\n                const methodHeader = pluginHeader === null || pluginHeader === void 0 ? void 0 : pluginHeader.methods.find((m) => prop === m.name);\n                if (methodHeader) {\n                    if (methodHeader.rtype === 'promise') {\n                        return (options) => cap.nativePromise(pluginName, prop.toString(), options);\n                    }\n                    else {\n                        return (options, callback) => cap.nativeCallback(pluginName, prop.toString(), options, callback);\n                    }\n                }\n                else if (impl) {\n                    return (_a = impl[prop]) === null || _a === void 0 ? void 0 : _a.bind(impl);\n                }\n            }\n            else if (impl) {\n                return (_b = impl[prop]) === null || _b === void 0 ? void 0 : _b.bind(impl);\n            }\n            else {\n                throw new CapacitorException(`\"${pluginName}\" plugin is not implemented on ${platform}`, ExceptionCode.Unimplemented);\n            }\n        };\n        const createPluginMethodWrapper = (prop) => {\n            let remove;\n            const wrapper = (...args) => {\n                const p = loadPluginImplementation().then((impl) => {\n                    const fn = createPluginMethod(impl, prop);\n                    if (fn) {\n                        const p = fn(...args);\n                        remove = p === null || p === void 0 ? void 0 : p.remove;\n                        return p;\n                    }\n                    else {\n                        throw new CapacitorException(`\"${pluginName}.${prop}()\" is not implemented on ${platform}`, ExceptionCode.Unimplemented);\n                    }\n                });\n                if (prop === 'addListener') {\n                    p.remove = async () => remove();\n                }\n                return p;\n            };\n            // Some flair ✨\n            wrapper.toString = () => `${prop.toString()}() { [capacitor code] }`;\n            Object.defineProperty(wrapper, 'name', {\n                value: prop,\n                writable: false,\n                configurable: false,\n            });\n            return wrapper;\n        };\n        const addListener = createPluginMethodWrapper('addListener');\n        const removeListener = createPluginMethodWrapper('removeListener');\n        const addListenerNative = (eventName, callback) => {\n            const call = addListener({ eventName }, callback);\n            const remove = async () => {\n                const callbackId = await call;\n                removeListener({\n                    eventName,\n                    callbackId,\n                }, callback);\n            };\n            const p = new Promise((resolve) => call.then(() => resolve({ remove })));\n            p.remove = async () => {\n                console.warn(`Using addListener() without 'await' is deprecated.`);\n                await remove();\n            };\n            return p;\n        };\n        const proxy = new Proxy({}, {\n            get(_, prop) {\n                switch (prop) {\n                    // https://github.com/facebook/react/issues/20030\n                    case '$$typeof':\n                        return undefined;\n                    case 'toJSON':\n                        return () => ({});\n                    case 'addListener':\n                        return pluginHeader ? addListenerNative : addListener;\n                    case 'removeListener':\n                        return removeListener;\n                    default:\n                        return createPluginMethodWrapper(prop);\n                }\n            },\n        });\n        Plugins[pluginName] = proxy;\n        registeredPlugins.set(pluginName, {\n            name: pluginName,\n            proxy,\n            platforms: new Set([...Object.keys(jsImplementations), ...(pluginHeader ? [platform] : [])]),\n        });\n        return proxy;\n    };\n    // Add in convertFileSrc for web, it will already be available in native context\n    if (!cap.convertFileSrc) {\n        cap.convertFileSrc = (filePath) => filePath;\n    }\n    cap.getPlatform = getPlatform;\n    cap.handleError = handleError;\n    cap.isNativePlatform = isNativePlatform;\n    cap.isPluginAvailable = isPluginAvailable;\n    cap.registerPlugin = registerPlugin;\n    cap.Exception = CapacitorException;\n    cap.DEBUG = !!cap.DEBUG;\n    cap.isLoggingEnabled = !!cap.isLoggingEnabled;\n    return cap;\n};\nexport const initCapacitorGlobal = (win) => (win.Capacitor = createCapacitor(win));\n//# sourceMappingURL=runtime.js.map", "import { initCapacitorGlobal } from './runtime';\nexport const Capacitor = /*#__PURE__*/ initCapacitorGlobal(typeof globalThis !== 'undefined'\n    ? globalThis\n    : typeof self !== 'undefined'\n        ? self\n        : typeof window !== 'undefined'\n            ? window\n            : typeof global !== 'undefined'\n                ? global\n                : {});\nexport const registerPlugin = Capacitor.registerPlugin;\n//# sourceMappingURL=global.js.map", "import { Capacitor } from './global';\nimport { ExceptionCode } from './util';\n/**\n * Base class web plugins should extend.\n */\nexport class WebPlugin {\n    constructor() {\n        this.listeners = {};\n        this.retainedEventArguments = {};\n        this.windowListeners = {};\n    }\n    addListener(eventName, listenerFunc) {\n        let firstListener = false;\n        const listeners = this.listeners[eventName];\n        if (!listeners) {\n            this.listeners[eventName] = [];\n            firstListener = true;\n        }\n        this.listeners[eventName].push(listenerFunc);\n        // If we haven't added a window listener for this event and it requires one,\n        // go ahead and add it\n        const windowListener = this.windowListeners[eventName];\n        if (windowListener && !windowListener.registered) {\n            this.addWindowListener(windowListener);\n        }\n        if (firstListener) {\n            this.sendRetainedArgumentsForEvent(eventName);\n        }\n        const remove = async () => this.removeListener(eventName, listenerFunc);\n        const p = Promise.resolve({ remove });\n        return p;\n    }\n    async removeAllListeners() {\n        this.listeners = {};\n        for (const listener in this.windowListeners) {\n            this.removeWindowListener(this.windowListeners[listener]);\n        }\n        this.windowListeners = {};\n    }\n    notifyListeners(eventName, data, retainUntilConsumed) {\n        const listeners = this.listeners[eventName];\n        if (!listeners) {\n            if (retainUntilConsumed) {\n                let args = this.retainedEventArguments[eventName];\n                if (!args) {\n                    args = [];\n                }\n                args.push(data);\n                this.retainedEventArguments[eventName] = args;\n            }\n            return;\n        }\n        listeners.forEach((listener) => listener(data));\n    }\n    hasListeners(eventName) {\n        var _a;\n        return !!((_a = this.listeners[eventName]) === null || _a === void 0 ? void 0 : _a.length);\n    }\n    registerWindowListener(windowEventName, pluginEventName) {\n        this.windowListeners[pluginEventName] = {\n            registered: false,\n            windowEventName,\n            pluginEventName,\n            handler: (event) => {\n                this.notifyListeners(pluginEventName, event);\n            },\n        };\n    }\n    unimplemented(msg = 'not implemented') {\n        return new Capacitor.Exception(msg, ExceptionCode.Unimplemented);\n    }\n    unavailable(msg = 'not available') {\n        return new Capacitor.Exception(msg, ExceptionCode.Unavailable);\n    }\n    async removeListener(eventName, listenerFunc) {\n        const listeners = this.listeners[eventName];\n        if (!listeners) {\n            return;\n        }\n        const index = listeners.indexOf(listenerFunc);\n        this.listeners[eventName].splice(index, 1);\n        // If there are no more listeners for this type of event,\n        // remove the window listener\n        if (!this.listeners[eventName].length) {\n            this.removeWindowListener(this.windowListeners[eventName]);\n        }\n    }\n    addWindowListener(handle) {\n        window.addEventListener(handle.windowEventName, handle.handler);\n        handle.registered = true;\n    }\n    removeWindowListener(handle) {\n        if (!handle) {\n            return;\n        }\n        window.removeEventListener(handle.windowEventName, handle.handler);\n        handle.registered = false;\n    }\n    sendRetainedArgumentsForEvent(eventName) {\n        const args = this.retainedEventArguments[eventName];\n        if (!args) {\n            return;\n        }\n        delete this.retainedEventArguments[eventName];\n        args.forEach((arg) => {\n            this.notifyListeners(eventName, arg);\n        });\n    }\n}\n//# sourceMappingURL=web-plugin.js.map", "import { registerPlugin } from './global';\nimport { WebPlugin } from './web-plugin';\nexport const WebView = /*#__PURE__*/ registerPlugin('WebView');\n/******** END WEB VIEW PLUGIN ********/\n/******** COOKIES PLUGIN ********/\n/**\n * Safely web encode a string value (inspired by js-cookie)\n * @param str The string value to encode\n */\nconst encode = (str) => encodeURIComponent(str)\n    .replace(/%(2[346B]|5E|60|7C)/g, decodeURIComponent)\n    .replace(/[()]/g, escape);\n/**\n * Safely web decode a string value (inspired by js-cookie)\n * @param str The string value to decode\n */\nconst decode = (str) => str.replace(/(%[\\dA-F]{2})+/gi, decodeURIComponent);\nexport class CapacitorCookiesPluginWeb extends WebPlugin {\n    async getCookies() {\n        const cookies = document.cookie;\n        const cookieMap = {};\n        cookies.split(';').forEach((cookie) => {\n            if (cookie.length <= 0)\n                return;\n            // Replace first \"=\" with CAP_COOKIE to prevent splitting on additional \"=\"\n            let [key, value] = cookie.replace(/=/, 'CAP_COOKIE').split('CAP_COOKIE');\n            key = decode(key).trim();\n            value = decode(value).trim();\n            cookieMap[key] = value;\n        });\n        return cookieMap;\n    }\n    async setCookie(options) {\n        try {\n            // Safely Encoded Key/Value\n            const encodedKey = encode(options.key);\n            const encodedValue = encode(options.value);\n            // Clean & sanitize options\n            const expires = `; expires=${(options.expires || '').replace('expires=', '')}`; // Default is \"; expires=\"\n            const path = (options.path || '/').replace('path=', ''); // Default is \"path=/\"\n            const domain = options.url != null && options.url.length > 0 ? `domain=${options.url}` : '';\n            document.cookie = `${encodedKey}=${encodedValue || ''}${expires}; path=${path}; ${domain};`;\n        }\n        catch (error) {\n            return Promise.reject(error);\n        }\n    }\n    async deleteCookie(options) {\n        try {\n            document.cookie = `${options.key}=; Max-Age=0`;\n        }\n        catch (error) {\n            return Promise.reject(error);\n        }\n    }\n    async clearCookies() {\n        try {\n            const cookies = document.cookie.split(';') || [];\n            for (const cookie of cookies) {\n                document.cookie = cookie.replace(/^ +/, '').replace(/=.*/, `=;expires=${new Date().toUTCString()};path=/`);\n            }\n        }\n        catch (error) {\n            return Promise.reject(error);\n        }\n    }\n    async clearAllCookies() {\n        try {\n            await this.clearCookies();\n        }\n        catch (error) {\n            return Promise.reject(error);\n        }\n    }\n}\nexport const CapacitorCookies = registerPlugin('CapacitorCookies', {\n    web: () => new CapacitorCookiesPluginWeb(),\n});\n// UTILITY FUNCTIONS\n/**\n * Read in a Blob value and return it as a base64 string\n * @param blob The blob value to convert to a base64 string\n */\nexport const readBlobAsBase64 = async (blob) => new Promise((resolve, reject) => {\n    const reader = new FileReader();\n    reader.onload = () => {\n        const base64String = reader.result;\n        // remove prefix \"data:application/pdf;base64,\"\n        resolve(base64String.indexOf(',') >= 0 ? base64String.split(',')[1] : base64String);\n    };\n    reader.onerror = (error) => reject(error);\n    reader.readAsDataURL(blob);\n});\n/**\n * Normalize an HttpHeaders map by lowercasing all of the values\n * @param headers The HttpHeaders object to normalize\n */\nconst normalizeHttpHeaders = (headers = {}) => {\n    const originalKeys = Object.keys(headers);\n    const loweredKeys = Object.keys(headers).map((k) => k.toLocaleLowerCase());\n    const normalized = loweredKeys.reduce((acc, key, index) => {\n        acc[key] = headers[originalKeys[index]];\n        return acc;\n    }, {});\n    return normalized;\n};\n/**\n * Builds a string of url parameters that\n * @param params A map of url parameters\n * @param shouldEncode true if you should encodeURIComponent() the values (true by default)\n */\nconst buildUrlParams = (params, shouldEncode = true) => {\n    if (!params)\n        return null;\n    const output = Object.entries(params).reduce((accumulator, entry) => {\n        const [key, value] = entry;\n        let encodedValue;\n        let item;\n        if (Array.isArray(value)) {\n            item = '';\n            value.forEach((str) => {\n                encodedValue = shouldEncode ? encodeURIComponent(str) : str;\n                item += `${key}=${encodedValue}&`;\n            });\n            // last character will always be \"&\" so slice it off\n            item.slice(0, -1);\n        }\n        else {\n            encodedValue = shouldEncode ? encodeURIComponent(value) : value;\n            item = `${key}=${encodedValue}`;\n        }\n        return `${accumulator}&${item}`;\n    }, '');\n    // Remove initial \"&\" from the reduce\n    return output.substr(1);\n};\n/**\n * Build the RequestInit object based on the options passed into the initial request\n * @param options The Http plugin options\n * @param extra Any extra RequestInit values\n */\nexport const buildRequestInit = (options, extra = {}) => {\n    const output = Object.assign({ method: options.method || 'GET', headers: options.headers }, extra);\n    // Get the content-type\n    const headers = normalizeHttpHeaders(options.headers);\n    const type = headers['content-type'] || '';\n    // If body is already a string, then pass it through as-is.\n    if (typeof options.data === 'string') {\n        output.body = options.data;\n    }\n    // Build request initializers based off of content-type\n    else if (type.includes('application/x-www-form-urlencoded')) {\n        const params = new URLSearchParams();\n        for (const [key, value] of Object.entries(options.data || {})) {\n            params.set(key, value);\n        }\n        output.body = params.toString();\n    }\n    else if (type.includes('multipart/form-data') || options.data instanceof FormData) {\n        const form = new FormData();\n        if (options.data instanceof FormData) {\n            options.data.forEach((value, key) => {\n                form.append(key, value);\n            });\n        }\n        else {\n            for (const key of Object.keys(options.data)) {\n                form.append(key, options.data[key]);\n            }\n        }\n        output.body = form;\n        const headers = new Headers(output.headers);\n        headers.delete('content-type'); // content-type will be set by `window.fetch` to includy boundary\n        output.headers = headers;\n    }\n    else if (type.includes('application/json') || typeof options.data === 'object') {\n        output.body = JSON.stringify(options.data);\n    }\n    return output;\n};\n// WEB IMPLEMENTATION\nexport class CapacitorHttpPluginWeb extends WebPlugin {\n    /**\n     * Perform an Http request given a set of options\n     * @param options Options to build the HTTP request\n     */\n    async request(options) {\n        const requestInit = buildRequestInit(options, options.webFetchExtra);\n        const urlParams = buildUrlParams(options.params, options.shouldEncodeUrlParams);\n        const url = urlParams ? `${options.url}?${urlParams}` : options.url;\n        const response = await fetch(url, requestInit);\n        const contentType = response.headers.get('content-type') || '';\n        // Default to 'text' responseType so no parsing happens\n        let { responseType = 'text' } = response.ok ? options : {};\n        // If the response content-type is json, force the response to be json\n        if (contentType.includes('application/json')) {\n            responseType = 'json';\n        }\n        let data;\n        let blob;\n        switch (responseType) {\n            case 'arraybuffer':\n            case 'blob':\n                blob = await response.blob();\n                data = await readBlobAsBase64(blob);\n                break;\n            case 'json':\n                data = await response.json();\n                break;\n            case 'document':\n            case 'text':\n            default:\n                data = await response.text();\n        }\n        // Convert fetch headers to Capacitor HttpHeaders\n        const headers = {};\n        response.headers.forEach((value, key) => {\n            headers[key] = value;\n        });\n        return {\n            data,\n            headers,\n            status: response.status,\n            url: response.url,\n        };\n    }\n    /**\n     * Perform an Http GET request given a set of options\n     * @param options Options to build the HTTP request\n     */\n    async get(options) {\n        return this.request(Object.assign(Object.assign({}, options), { method: 'GET' }));\n    }\n    /**\n     * Perform an Http POST request given a set of options\n     * @param options Options to build the HTTP request\n     */\n    async post(options) {\n        return this.request(Object.assign(Object.assign({}, options), { method: 'POST' }));\n    }\n    /**\n     * Perform an Http PUT request given a set of options\n     * @param options Options to build the HTTP request\n     */\n    async put(options) {\n        return this.request(Object.assign(Object.assign({}, options), { method: 'PUT' }));\n    }\n    /**\n     * Perform an Http PATCH request given a set of options\n     * @param options Options to build the HTTP request\n     */\n    async patch(options) {\n        return this.request(Object.assign(Object.assign({}, options), { method: 'PATCH' }));\n    }\n    /**\n     * Perform an Http DELETE request given a set of options\n     * @param options Options to build the HTTP request\n     */\n    async delete(options) {\n        return this.request(Object.assign(Object.assign({}, options), { method: 'DELETE' }));\n    }\n}\nexport const CapacitorHttp = registerPlugin('CapacitorHttp', {\n    web: () => new CapacitorHttpPluginWeb(),\n});\n/******** END HTTP PLUGIN ********/\n//# sourceMappingURL=core-plugins.js.map"], "names": ["ExceptionCode"], "mappings": ";;;;;AAAWA,+BAAc;AACzB,CAAC,UAAU,aAAa,EAAE;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,aAAa,CAAC,eAAe,CAAC,GAAG,eAAe,CAAC;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,aAAa,CAAC,aAAa,CAAC,GAAG,aAAa,CAAC;AACjD,CAAC,EAAEA,qBAAa,KAAKA,qBAAa,GAAG,EAAE,CAAC,CAAC,CAAC;AACnC,MAAM,kBAAkB,SAAS,KAAK,CAAC;AAC9C,IAAI,WAAW,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE;AACrC,QAAQ,KAAK,CAAC,OAAO,CAAC,CAAC;AACvB,QAAQ,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AAC/B,QAAQ,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACzB,KAAK;AACL,CAAC;AACM,MAAM,aAAa,GAAG,CAAC,GAAG,KAAK;AACtC,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC;AACf,IAAI,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,aAAa,EAAE;AACrE,QAAQ,OAAO,SAAS,CAAC;AACzB,KAAK;AACL,SAAS,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,MAAM,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,eAAe,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE;AACxL,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK;AACL,SAAS;AACT,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK;AACL,CAAC;;ACpCM,MAAM,eAAe,GAAG,CAAC,GAAG,KAAK;AACxC,IAAI,MAAM,iBAAiB,GAAG,GAAG,CAAC,uBAAuB,IAAI,IAAI,CAAC;AAClE,IAAI,MAAM,GAAG,GAAG,GAAG,CAAC,SAAS,IAAI,EAAE,CAAC;AACpC,IAAI,MAAM,OAAO,IAAI,GAAG,CAAC,OAAO,GAAG,GAAG,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC;AACtD,IAAI,MAAM,WAAW,GAAG,MAAM;AAC9B,QAAQ,OAAO,iBAAiB,KAAK,IAAI,GAAG,iBAAiB,CAAC,IAAI,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;AACxF,KAAK,CAAC;AACN,IAAI,MAAM,gBAAgB,GAAG,MAAM,WAAW,EAAE,KAAK,KAAK,CAAC;AAC3D,IAAI,MAAM,iBAAiB,GAAG,CAAC,UAAU,KAAK;AAC9C,QAAQ,MAAM,MAAM,GAAG,iBAAiB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;AACzD,QAAQ,IAAI,MAAM,KAAK,IAAI,IAAI,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,EAAE;AACjG;AACA,YAAY,OAAO,IAAI,CAAC;AACxB,SAAS;AACT,QAAQ,IAAI,eAAe,CAAC,UAAU,CAAC,EAAE;AACzC;AACA,YAAY,OAAO,IAAI,CAAC;AACxB,SAAS;AACT,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK,CAAC;AACN,IAAI,MAAM,eAAe,GAAG,CAAC,UAAU,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE,GAAG,GAAG,CAAC,aAAa,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,UAAU,CAAC,CAAC,EAAE,CAAC;AACpK,IAAI,MAAM,WAAW,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACxD,IAAI,MAAM,iBAAiB,GAAG,IAAI,GAAG,EAAE,CAAC;AACxC,IAAI,MAAM,cAAc,GAAG,CAAC,UAAU,EAAE,iBAAiB,GAAG,EAAE,KAAK;AACnE,QAAQ,MAAM,gBAAgB,GAAG,iBAAiB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;AACnE,QAAQ,IAAI,gBAAgB,EAAE;AAC9B,YAAY,OAAO,CAAC,IAAI,CAAC,CAAC,kBAAkB,EAAE,UAAU,CAAC,oDAAoD,CAAC,CAAC,CAAC;AAChH,YAAY,OAAO,gBAAgB,CAAC,KAAK,CAAC;AAC1C,SAAS;AACT,QAAQ,MAAM,QAAQ,GAAG,WAAW,EAAE,CAAC;AACvC,QAAQ,MAAM,YAAY,GAAG,eAAe,CAAC,UAAU,CAAC,CAAC;AACzD,QAAQ,IAAI,gBAAgB,CAAC;AAC7B,QAAQ,MAAM,wBAAwB,GAAG,YAAY;AACrD,YAAY,IAAI,CAAC,gBAAgB,IAAI,QAAQ,IAAI,iBAAiB,EAAE;AACpE,gBAAgB,gBAAgB;AAChC,oBAAoB,OAAO,iBAAiB,CAAC,QAAQ,CAAC,KAAK,UAAU;AACrE,2BAA2B,gBAAgB,GAAG,MAAM,iBAAiB,CAAC,QAAQ,CAAC,EAAE;AACjF,2BAA2B,gBAAgB,GAAG,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC;AAC3E,aAAa;AACb,iBAAiB,IAAI,iBAAiB,KAAK,IAAI,IAAI,CAAC,gBAAgB,IAAI,KAAK,IAAI,iBAAiB,EAAE;AACpG,gBAAgB,gBAAgB;AAChC,oBAAoB,OAAO,iBAAiB,CAAC,KAAK,CAAC,KAAK,UAAU;AAClE,2BAA2B,gBAAgB,GAAG,MAAM,iBAAiB,CAAC,KAAK,CAAC,EAAE;AAC9E,2BAA2B,gBAAgB,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC;AACxE,aAAa;AACb,YAAY,OAAO,gBAAgB,CAAC;AACpC,SAAS,CAAC;AACV,QAAQ,MAAM,kBAAkB,GAAG,CAAC,IAAI,EAAE,IAAI,KAAK;AACnD,YAAY,IAAI,EAAE,EAAE,EAAE,CAAC;AACvB,YAAY,IAAI,YAAY,EAAE;AAC9B,gBAAgB,MAAM,YAAY,GAAG,YAAY,KAAK,IAAI,IAAI,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC;AACnJ,gBAAgB,IAAI,YAAY,EAAE;AAClC,oBAAoB,IAAI,YAAY,CAAC,KAAK,KAAK,SAAS,EAAE;AAC1D,wBAAwB,OAAO,CAAC,OAAO,KAAK,GAAG,CAAC,aAAa,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,OAAO,CAAC,CAAC;AACpG,qBAAqB;AACrB,yBAAyB;AACzB,wBAAwB,OAAO,CAAC,OAAO,EAAE,QAAQ,KAAK,GAAG,CAAC,cAAc,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;AACzH,qBAAqB;AACrB,iBAAiB;AACjB,qBAAqB,IAAI,IAAI,EAAE;AAC/B,oBAAoB,OAAO,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAChG,iBAAiB;AACjB,aAAa;AACb,iBAAiB,IAAI,IAAI,EAAE;AAC3B,gBAAgB,OAAO,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC5F,aAAa;AACb,iBAAiB;AACjB,gBAAgB,MAAM,IAAI,kBAAkB,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,+BAA+B,EAAE,QAAQ,CAAC,CAAC,EAAEA,qBAAa,CAAC,aAAa,CAAC,CAAC;AACtI,aAAa;AACb,SAAS,CAAC;AACV,QAAQ,MAAM,yBAAyB,GAAG,CAAC,IAAI,KAAK;AACpD,YAAY,IAAI,MAAM,CAAC;AACvB,YAAY,MAAM,OAAO,GAAG,CAAC,GAAG,IAAI,KAAK;AACzC,gBAAgB,MAAM,CAAC,GAAG,wBAAwB,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK;AACpE,oBAAoB,MAAM,EAAE,GAAG,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAC9D,oBAAoB,IAAI,EAAE,EAAE;AAC5B,wBAAwB,MAAM,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;AAC9C,wBAAwB,MAAM,GAAG,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;AAChF,wBAAwB,OAAO,CAAC,CAAC;AACjC,qBAAqB;AACrB,yBAAyB;AACzB,wBAAwB,MAAM,IAAI,kBAAkB,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC,0BAA0B,EAAE,QAAQ,CAAC,CAAC,EAAEA,qBAAa,CAAC,aAAa,CAAC,CAAC;AACjJ,qBAAqB;AACrB,iBAAiB,CAAC,CAAC;AACnB,gBAAgB,IAAI,IAAI,KAAK,aAAa,EAAE;AAC5C,oBAAoB,CAAC,CAAC,MAAM,GAAG,YAAY,MAAM,EAAE,CAAC;AACpD,iBAAiB;AACjB,gBAAgB,OAAO,CAAC,CAAC;AACzB,aAAa,CAAC;AACd;AACA,YAAY,OAAO,CAAC,QAAQ,GAAG,MAAM,CAAC,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,uBAAuB,CAAC,CAAC;AACjF,YAAY,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,MAAM,EAAE;AACnD,gBAAgB,KAAK,EAAE,IAAI;AAC3B,gBAAgB,QAAQ,EAAE,KAAK;AAC/B,gBAAgB,YAAY,EAAE,KAAK;AACnC,aAAa,CAAC,CAAC;AACf,YAAY,OAAO,OAAO,CAAC;AAC3B,SAAS,CAAC;AACV,QAAQ,MAAM,WAAW,GAAG,yBAAyB,CAAC,aAAa,CAAC,CAAC;AACrE,QAAQ,MAAM,cAAc,GAAG,yBAAyB,CAAC,gBAAgB,CAAC,CAAC;AAC3E,QAAQ,MAAM,iBAAiB,GAAG,CAAC,SAAS,EAAE,QAAQ,KAAK;AAC3D,YAAY,MAAM,IAAI,GAAG,WAAW,CAAC,EAAE,SAAS,EAAE,EAAE,QAAQ,CAAC,CAAC;AAC9D,YAAY,MAAM,MAAM,GAAG,YAAY;AACvC,gBAAgB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC;AAC9C,gBAAgB,cAAc,CAAC;AAC/B,oBAAoB,SAAS;AAC7B,oBAAoB,UAAU;AAC9B,iBAAiB,EAAE,QAAQ,CAAC,CAAC;AAC7B,aAAa,CAAC;AACd,YAAY,MAAM,CAAC,GAAG,IAAI,OAAO,CAAC,CAAC,OAAO,KAAK,IAAI,CAAC,IAAI,CAAC,MAAM,OAAO,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;AACrF,YAAY,CAAC,CAAC,MAAM,GAAG,YAAY;AACnC,gBAAgB,OAAO,CAAC,IAAI,CAAC,CAAC,kDAAkD,CAAC,CAAC,CAAC;AACnF,gBAAgB,MAAM,MAAM,EAAE,CAAC;AAC/B,aAAa,CAAC;AACd,YAAY,OAAO,CAAC,CAAC;AACrB,SAAS,CAAC;AACV,QAAQ,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,EAAE,EAAE;AACpC,YAAY,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE;AACzB,gBAAgB,QAAQ,IAAI;AAC5B;AACA,oBAAoB,KAAK,UAAU;AACnC,wBAAwB,OAAO,SAAS,CAAC;AACzC,oBAAoB,KAAK,QAAQ;AACjC,wBAAwB,OAAO,OAAO,EAAE,CAAC,CAAC;AAC1C,oBAAoB,KAAK,aAAa;AACtC,wBAAwB,OAAO,YAAY,GAAG,iBAAiB,GAAG,WAAW,CAAC;AAC9E,oBAAoB,KAAK,gBAAgB;AACzC,wBAAwB,OAAO,cAAc,CAAC;AAC9C,oBAAoB;AACpB,wBAAwB,OAAO,yBAAyB,CAAC,IAAI,CAAC,CAAC;AAC/D,iBAAiB;AACjB,aAAa;AACb,SAAS,CAAC,CAAC;AACX,QAAQ,OAAO,CAAC,UAAU,CAAC,GAAG,KAAK,CAAC;AACpC,QAAQ,iBAAiB,CAAC,GAAG,CAAC,UAAU,EAAE;AAC1C,YAAY,IAAI,EAAE,UAAU;AAC5B,YAAY,KAAK;AACjB,YAAY,SAAS,EAAE,IAAI,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE,IAAI,YAAY,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AACxG,SAAS,CAAC,CAAC;AACX,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK,CAAC;AACN;AACA,IAAI,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE;AAC7B,QAAQ,GAAG,CAAC,cAAc,GAAG,CAAC,QAAQ,KAAK,QAAQ,CAAC;AACpD,KAAK;AACL,IAAI,GAAG,CAAC,WAAW,GAAG,WAAW,CAAC;AAClC,IAAI,GAAG,CAAC,WAAW,GAAG,WAAW,CAAC;AAClC,IAAI,GAAG,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;AAC5C,IAAI,GAAG,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;AAC9C,IAAI,GAAG,CAAC,cAAc,GAAG,cAAc,CAAC;AACxC,IAAI,GAAG,CAAC,SAAS,GAAG,kBAAkB,CAAC;AACvC,IAAI,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC;AAC5B,IAAI,GAAG,CAAC,gBAAgB,GAAG,CAAC,CAAC,GAAG,CAAC,gBAAgB,CAAC;AAClD,IAAI,OAAO,GAAG,CAAC;AACf,CAAC,CAAC;AACK,MAAM,mBAAmB,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,SAAS,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC;;AC3JtE,MAAC,SAAS,iBAAiB,mBAAmB,CAAC,OAAO,UAAU,KAAK,WAAW;AAC5F,MAAM,UAAU;AAChB,MAAM,OAAO,IAAI,KAAK,WAAW;AACjC,UAAU,IAAI;AACd,UAAU,OAAO,MAAM,KAAK,WAAW;AACvC,cAAc,MAAM;AACpB,cAAc,OAAO,MAAM,KAAK,WAAW;AAC3C,kBAAkB,MAAM;AACxB,kBAAkB,EAAE,EAAE;AACV,MAAC,cAAc,GAAG,SAAS,CAAC;;ACRxC;AACA;AACA;AACO,MAAM,SAAS,CAAC;AACvB,IAAI,WAAW,GAAG;AAClB,QAAQ,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;AAC5B,QAAQ,IAAI,CAAC,sBAAsB,GAAG,EAAE,CAAC;AACzC,QAAQ,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;AAClC,KAAK;AACL,IAAI,WAAW,CAAC,SAAS,EAAE,YAAY,EAAE;AACzC,QAAQ,IAAI,aAAa,GAAG,KAAK,CAAC;AAClC,QAAQ,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpD,QAAQ,IAAI,CAAC,SAAS,EAAE;AACxB,YAAY,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;AAC3C,YAAY,aAAa,GAAG,IAAI,CAAC;AACjC,SAAS;AACT,QAAQ,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AACrD;AACA;AACA,QAAQ,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;AAC/D,QAAQ,IAAI,cAAc,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE;AAC1D,YAAY,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;AACnD,SAAS;AACT,QAAQ,IAAI,aAAa,EAAE;AAC3B,YAAY,IAAI,CAAC,6BAA6B,CAAC,SAAS,CAAC,CAAC;AAC1D,SAAS;AACT,QAAQ,MAAM,MAAM,GAAG,YAAY,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;AAChF,QAAQ,MAAM,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;AAC9C,QAAQ,OAAO,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,MAAM,kBAAkB,GAAG;AAC/B,QAAQ,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;AAC5B,QAAQ,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,eAAe,EAAE;AACrD,YAAY,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;AACtE,SAAS;AACT,QAAQ,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;AAClC,KAAK;AACL,IAAI,eAAe,CAAC,SAAS,EAAE,IAAI,EAAE,mBAAmB,EAAE;AAC1D,QAAQ,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpD,QAAQ,IAAI,CAAC,SAAS,EAAE;AACxB,YAAY,IAAI,mBAAmB,EAAE;AACrC,gBAAgB,IAAI,IAAI,GAAG,IAAI,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;AAClE,gBAAgB,IAAI,CAAC,IAAI,EAAE;AAC3B,oBAAoB,IAAI,GAAG,EAAE,CAAC;AAC9B,iBAAiB;AACjB,gBAAgB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAChC,gBAAgB,IAAI,CAAC,sBAAsB,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC;AAC9D,aAAa;AACb,YAAY,OAAO;AACnB,SAAS;AACT,QAAQ,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;AACxD,KAAK;AACL,IAAI,YAAY,CAAC,SAAS,EAAE;AAC5B,QAAQ,IAAI,EAAE,CAAC;AACf,QAAQ,OAAO,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC;AACnG,KAAK;AACL,IAAI,sBAAsB,CAAC,eAAe,EAAE,eAAe,EAAE;AAC7D,QAAQ,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,GAAG;AAChD,YAAY,UAAU,EAAE,KAAK;AAC7B,YAAY,eAAe;AAC3B,YAAY,eAAe;AAC3B,YAAY,OAAO,EAAE,CAAC,KAAK,KAAK;AAChC,gBAAgB,IAAI,CAAC,eAAe,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;AAC7D,aAAa;AACb,SAAS,CAAC;AACV,KAAK;AACL,IAAI,aAAa,CAAC,GAAG,GAAG,iBAAiB,EAAE;AAC3C,QAAQ,OAAO,IAAI,SAAS,CAAC,SAAS,CAAC,GAAG,EAAEA,qBAAa,CAAC,aAAa,CAAC,CAAC;AACzE,KAAK;AACL,IAAI,WAAW,CAAC,GAAG,GAAG,eAAe,EAAE;AACvC,QAAQ,OAAO,IAAI,SAAS,CAAC,SAAS,CAAC,GAAG,EAAEA,qBAAa,CAAC,WAAW,CAAC,CAAC;AACvE,KAAK;AACL,IAAI,MAAM,cAAc,CAAC,SAAS,EAAE,YAAY,EAAE;AAClD,QAAQ,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpD,QAAQ,IAAI,CAAC,SAAS,EAAE;AACxB,YAAY,OAAO;AACnB,SAAS;AACT,QAAQ,MAAM,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;AACtD,QAAQ,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AACnD;AACA;AACA,QAAQ,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,MAAM,EAAE;AAC/C,YAAY,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,CAAC;AACvE,SAAS;AACT,KAAK;AACL,IAAI,iBAAiB,CAAC,MAAM,EAAE;AAC9B,QAAQ,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,eAAe,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;AACxE,QAAQ,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC;AACjC,KAAK;AACL,IAAI,oBAAoB,CAAC,MAAM,EAAE;AACjC,QAAQ,IAAI,CAAC,MAAM,EAAE;AACrB,YAAY,OAAO;AACnB,SAAS;AACT,QAAQ,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,eAAe,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;AAC3E,QAAQ,MAAM,CAAC,UAAU,GAAG,KAAK,CAAC;AAClC,KAAK;AACL,IAAI,6BAA6B,CAAC,SAAS,EAAE;AAC7C,QAAQ,MAAM,IAAI,GAAG,IAAI,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;AAC5D,QAAQ,IAAI,CAAC,IAAI,EAAE;AACnB,YAAY,OAAO;AACnB,SAAS;AACT,QAAQ,OAAO,IAAI,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;AACtD,QAAQ,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK;AAC9B,YAAY,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;AACjD,SAAS,CAAC,CAAC;AACX,KAAK;AACL;;AC1GY,MAAC,OAAO,iBAAiB,cAAc,CAAC,SAAS,EAAE;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,MAAM,GAAG,CAAC,GAAG,KAAK,kBAAkB,CAAC,GAAG,CAAC;AAC/C,KAAK,OAAO,CAAC,sBAAsB,EAAE,kBAAkB,CAAC;AACxD,KAAK,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;AAC9B;AACA;AACA;AACA;AACA,MAAM,MAAM,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,OAAO,CAAC,kBAAkB,EAAE,kBAAkB,CAAC,CAAC;AACrE,MAAM,yBAAyB,SAAS,SAAS,CAAC;AACzD,IAAI,MAAM,UAAU,GAAG;AACvB,QAAQ,MAAM,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC;AACxC,QAAQ,MAAM,SAAS,GAAG,EAAE,CAAC;AAC7B,QAAQ,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK;AAC/C,YAAY,IAAI,MAAM,CAAC,MAAM,IAAI,CAAC;AAClC,gBAAgB,OAAO;AACvB;AACA,YAAY,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;AACrF,YAAY,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;AACrC,YAAY,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC;AACzC,YAAY,SAAS,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AACnC,SAAS,CAAC,CAAC;AACX,QAAQ,OAAO,SAAS,CAAC;AACzB,KAAK;AACL,IAAI,MAAM,SAAS,CAAC,OAAO,EAAE;AAC7B,QAAQ,IAAI;AACZ;AACA,YAAY,MAAM,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AACnD,YAAY,MAAM,YAAY,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACvD;AACA,YAAY,MAAM,OAAO,GAAG,CAAC,UAAU,EAAE,CAAC,OAAO,CAAC,OAAO,IAAI,EAAE,EAAE,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AAC3F,YAAY,MAAM,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,IAAI,GAAG,EAAE,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;AACpE,YAAY,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,IAAI,IAAI,IAAI,OAAO,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;AACxG,YAAY,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,UAAU,CAAC,CAAC,EAAE,YAAY,IAAI,EAAE,CAAC,EAAE,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;AACxG,SAAS;AACT,QAAQ,OAAO,KAAK,EAAE;AACtB,YAAY,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACzC,SAAS;AACT,KAAK;AACL,IAAI,MAAM,YAAY,CAAC,OAAO,EAAE;AAChC,QAAQ,IAAI;AACZ,YAAY,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;AAC3D,SAAS;AACT,QAAQ,OAAO,KAAK,EAAE;AACtB,YAAY,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACzC,SAAS;AACT,KAAK;AACL,IAAI,MAAM,YAAY,GAAG;AACzB,QAAQ,IAAI;AACZ,YAAY,MAAM,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;AAC7D,YAAY,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;AAC1C,gBAAgB,QAAQ,CAAC,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;AAC3H,aAAa;AACb,SAAS;AACT,QAAQ,OAAO,KAAK,EAAE;AACtB,YAAY,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACzC,SAAS;AACT,KAAK;AACL,IAAI,MAAM,eAAe,GAAG;AAC5B,QAAQ,IAAI;AACZ,YAAY,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;AACtC,SAAS;AACT,QAAQ,OAAO,KAAK,EAAE;AACtB,YAAY,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACzC,SAAS;AACT,KAAK;AACL,CAAC;AACW,MAAC,gBAAgB,GAAG,cAAc,CAAC,kBAAkB,EAAE;AACnE,IAAI,GAAG,EAAE,MAAM,IAAI,yBAAyB,EAAE;AAC9C,CAAC,EAAE;AACH;AACA;AACA;AACA;AACA;AACO,MAAM,gBAAgB,GAAG,OAAO,IAAI,KAAK,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAK;AACjF,IAAI,MAAM,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;AACpC,IAAI,MAAM,CAAC,MAAM,GAAG,MAAM;AAC1B,QAAQ,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC;AAC3C;AACA,QAAQ,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC;AAC5F,KAAK,CAAC;AACN,IAAI,MAAM,CAAC,OAAO,GAAG,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC;AAC9C,IAAI,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;AAC/B,CAAC,CAAC,CAAC;AACH;AACA;AACA;AACA;AACA,MAAM,oBAAoB,GAAG,CAAC,OAAO,GAAG,EAAE,KAAK;AAC/C,IAAI,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC9C,IAAI,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,iBAAiB,EAAE,CAAC,CAAC;AAC/E,IAAI,MAAM,UAAU,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,KAAK,KAAK;AAC/D,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;AAChD,QAAQ,OAAO,GAAG,CAAC;AACnB,KAAK,EAAE,EAAE,CAAC,CAAC;AACX,IAAI,OAAO,UAAU,CAAC;AACtB,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,MAAM,cAAc,GAAG,CAAC,MAAM,EAAE,YAAY,GAAG,IAAI,KAAK;AACxD,IAAI,IAAI,CAAC,MAAM;AACf,QAAQ,OAAO,IAAI,CAAC;AACpB,IAAI,MAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,KAAK,KAAK;AACzE,QAAQ,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,KAAK,CAAC;AACnC,QAAQ,IAAI,YAAY,CAAC;AACzB,QAAQ,IAAI,IAAI,CAAC;AACjB,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AAClC,YAAY,IAAI,GAAG,EAAE,CAAC;AACtB,YAAY,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK;AACnC,gBAAgB,YAAY,GAAG,YAAY,GAAG,kBAAkB,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;AAC5E,gBAAgB,IAAI,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;AAClD,aAAa,CAAC,CAAC;AACf;AACA,YAAY,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC9B,SAAS;AACT,aAAa;AACb,YAAY,YAAY,GAAG,YAAY,GAAG,kBAAkB,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;AAC5E,YAAY,IAAI,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC;AAC5C,SAAS;AACT,QAAQ,OAAO,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;AACxC,KAAK,EAAE,EAAE,CAAC,CAAC;AACX;AACA,IAAI,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC5B,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACY,MAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;AACzD,IAAI,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,CAAC;AACvG;AACA,IAAI,MAAM,OAAO,GAAG,oBAAoB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAC1D,IAAI,MAAM,IAAI,GAAG,OAAO,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;AAC/C;AACA,IAAI,IAAI,OAAO,OAAO,CAAC,IAAI,KAAK,QAAQ,EAAE;AAC1C,QAAQ,MAAM,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;AACnC,KAAK;AACL;AACA,SAAS,IAAI,IAAI,CAAC,QAAQ,CAAC,mCAAmC,CAAC,EAAE;AACjE,QAAQ,MAAM,MAAM,GAAG,IAAI,eAAe,EAAE,CAAC;AAC7C,QAAQ,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE,CAAC,EAAE;AACvE,YAAY,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AACnC,SAAS;AACT,QAAQ,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;AACxC,KAAK;AACL,SAAS,IAAI,IAAI,CAAC,QAAQ,CAAC,qBAAqB,CAAC,IAAI,OAAO,CAAC,IAAI,YAAY,QAAQ,EAAE;AACvF,QAAQ,MAAM,IAAI,GAAG,IAAI,QAAQ,EAAE,CAAC;AACpC,QAAQ,IAAI,OAAO,CAAC,IAAI,YAAY,QAAQ,EAAE;AAC9C,YAAY,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACjD,gBAAgB,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AACxC,aAAa,CAAC,CAAC;AACf,SAAS;AACT,aAAa;AACb,YAAY,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;AACzD,gBAAgB,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AACpD,aAAa;AACb,SAAS;AACT,QAAQ,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;AAC3B,QAAQ,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AACpD,QAAQ,OAAO,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;AACvC,QAAQ,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC;AACjC,KAAK;AACL,SAAS,IAAI,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,IAAI,OAAO,OAAO,CAAC,IAAI,KAAK,QAAQ,EAAE;AACpF,QAAQ,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACnD,KAAK;AACL,IAAI,OAAO,MAAM,CAAC;AAClB,EAAE;AACF;AACO,MAAM,sBAAsB,SAAS,SAAS,CAAC;AACtD;AACA;AACA;AACA;AACA,IAAI,MAAM,OAAO,CAAC,OAAO,EAAE;AAC3B,QAAQ,MAAM,WAAW,GAAG,gBAAgB,CAAC,OAAO,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;AAC7E,QAAQ,MAAM,SAAS,GAAG,cAAc,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,qBAAqB,CAAC,CAAC;AACxF,QAAQ,MAAM,GAAG,GAAG,SAAS,GAAG,CAAC,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC;AAC5E,QAAQ,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;AACvD,QAAQ,MAAM,WAAW,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;AACvE;AACA,QAAQ,IAAI,EAAE,YAAY,GAAG,MAAM,EAAE,GAAG,QAAQ,CAAC,EAAE,GAAG,OAAO,GAAG,EAAE,CAAC;AACnE;AACA,QAAQ,IAAI,WAAW,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE;AACtD,YAAY,YAAY,GAAG,MAAM,CAAC;AAClC,SAAS;AACT,QAAQ,IAAI,IAAI,CAAC;AACjB,QAAQ,IAAI,IAAI,CAAC;AACjB,QAAQ,QAAQ,YAAY;AAC5B,YAAY,KAAK,aAAa,CAAC;AAC/B,YAAY,KAAK,MAAM;AACvB,gBAAgB,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;AAC7C,gBAAgB,IAAI,GAAG,MAAM,gBAAgB,CAAC,IAAI,CAAC,CAAC;AACpD,gBAAgB,MAAM;AACtB,YAAY,KAAK,MAAM;AACvB,gBAAgB,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;AAC7C,gBAAgB,MAAM;AACtB,YAAY,KAAK,UAAU,CAAC;AAC5B,YAAY,KAAK,MAAM,CAAC;AACxB,YAAY;AACZ,gBAAgB,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;AAC7C,SAAS;AACT;AACA,QAAQ,MAAM,OAAO,GAAG,EAAE,CAAC;AAC3B,QAAQ,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,GAAG,KAAK;AACjD,YAAY,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AACjC,SAAS,CAAC,CAAC;AACX,QAAQ,OAAO;AACf,YAAY,IAAI;AAChB,YAAY,OAAO;AACnB,YAAY,MAAM,EAAE,QAAQ,CAAC,MAAM;AACnC,YAAY,GAAG,EAAE,QAAQ,CAAC,GAAG;AAC7B,SAAS,CAAC;AACV,KAAK;AACL;AACA;AACA;AACA;AACA,IAAI,MAAM,GAAG,CAAC,OAAO,EAAE;AACvB,QAAQ,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;AAC1F,KAAK;AACL;AACA;AACA;AACA;AACA,IAAI,MAAM,IAAI,CAAC,OAAO,EAAE;AACxB,QAAQ,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;AAC3F,KAAK;AACL;AACA;AACA;AACA;AACA,IAAI,MAAM,GAAG,CAAC,OAAO,EAAE;AACvB,QAAQ,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;AAC1F,KAAK;AACL;AACA;AACA;AACA;AACA,IAAI,MAAM,KAAK,CAAC,OAAO,EAAE;AACzB,QAAQ,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;AAC5F,KAAK;AACL;AACA;AACA;AACA;AACA,IAAI,MAAM,MAAM,CAAC,OAAO,EAAE;AAC1B,QAAQ,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC;AAC7F,KAAK;AACL,CAAC;AACW,MAAC,aAAa,GAAG,cAAc,CAAC,eAAe,EAAE;AAC7D,IAAI,GAAG,EAAE,MAAM,IAAI,sBAAsB,EAAE;AAC3C,CAAC,EAAE;AACH;;;;;;;;;;;"}