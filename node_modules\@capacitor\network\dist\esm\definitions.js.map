{"version": 3, "file": "definitions.js", "sourceRoot": "", "sources": ["../../src/definitions.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { PluginListenerHandle } from '@capacitor/core';\n\nexport interface NetworkPlugin {\n  /**\n   * Query the current status of the network connection.\n   *\n   * @since 1.0.0\n   */\n  getStatus(): Promise<ConnectionStatus>;\n\n  /**\n   * Listen for changes in the network connection.\n   *\n   * @since 1.0.0\n   */\n  addListener(\n    eventName: 'networkStatusChange',\n    listenerFunc: ConnectionStatusChangeListener,\n  ): Promise<PluginListenerHandle>;\n\n  /**\n   * Remove all listeners (including the network status changes) for this plugin.\n   *\n   * @since 1.0.0\n   */\n  removeAllListeners(): Promise<void>;\n}\n\n/**\n * Represents the state and type of the network connection.\n *\n * @since 1.0.0\n */\nexport interface ConnectionStatus {\n  /**\n   * Whether there is an active connection or not.\n   *\n   * @since 1.0.0\n   */\n  connected: boolean;\n\n  /**\n   * The type of network connection currently in use.\n   *\n   * If there is no active network connection, `connectionType` will be `'none'`.\n   *\n   * @since 1.0.0\n   */\n  connectionType: ConnectionType;\n}\n\n/**\n * Callback to receive the status change notifications.\n *\n * @since 1.0.0\n */\nexport type ConnectionStatusChangeListener = (status: ConnectionStatus) => void;\n\n/**\n * The type of network connection that a device might have.\n *\n * @since 1.0.0\n */\nexport type ConnectionType = 'wifi' | 'cellular' | 'none' | 'unknown';\n\n/**\n * @deprecated Use `ConnectionStatus`.\n * @since 1.0.0\n */\nexport type NetworkStatus = ConnectionStatus;\n\n/**\n * @deprecated Use `ConnectionStatusChangeListener`.\n * @since 1.0.0\n */\nexport type NetworkStatusChangeCallback = ConnectionStatusChangeListener;\n"]}