{"version": 3, "file": "plugin.js", "sources": ["esm/definitions.js", "esm/index.js", "esm/web.js"], "sourcesContent": ["export var ImpactStyle;\n(function (ImpactStyle) {\n    /**\n     * A collision between large, heavy user interface elements\n     *\n     * @since 1.0.0\n     */\n    ImpactStyle[\"Heavy\"] = \"HEAVY\";\n    /**\n     * A collision between moderately sized user interface elements\n     *\n     * @since 1.0.0\n     */\n    ImpactStyle[\"Medium\"] = \"MEDIUM\";\n    /**\n     * A collision between small, light user interface elements\n     *\n     * @since 1.0.0\n     */\n    ImpactStyle[\"Light\"] = \"LIGHT\";\n})(ImpactStyle || (ImpactStyle = {}));\nexport var NotificationType;\n(function (NotificationType) {\n    /**\n     * A notification feedback type indicating that a task has completed successfully\n     *\n     * @since 1.0.0\n     */\n    NotificationType[\"Success\"] = \"SUCCESS\";\n    /**\n     * A notification feedback type indicating that a task has produced a warning\n     *\n     * @since 1.0.0\n     */\n    NotificationType[\"Warning\"] = \"WARNING\";\n    /**\n     * A notification feedback type indicating that a task has failed\n     *\n     * @since 1.0.0\n     */\n    NotificationType[\"Error\"] = \"ERROR\";\n})(NotificationType || (NotificationType = {}));\n//# sourceMappingURL=definitions.js.map", "import { registerPlugin } from '@capacitor/core';\nconst Haptics = registerPlugin('Haptics', {\n    web: () => import('./web').then(m => new m.HapticsWeb()),\n});\nexport * from './definitions';\nexport { Haptics };\n//# sourceMappingURL=index.js.map", "import { WebPlugin } from '@capacitor/core';\nimport { ImpactStyle, NotificationType } from './definitions';\nexport class HapticsWeb extends WebPlugin {\n    constructor() {\n        super(...arguments);\n        this.selectionStarted = false;\n    }\n    async impact(options) {\n        const pattern = this.patternForImpact(options === null || options === void 0 ? void 0 : options.style);\n        this.vibrateWithPattern(pattern);\n    }\n    async notification(options) {\n        const pattern = this.patternForNotification(options === null || options === void 0 ? void 0 : options.type);\n        this.vibrateWithPattern(pattern);\n    }\n    async vibrate(options) {\n        const duration = (options === null || options === void 0 ? void 0 : options.duration) || 300;\n        this.vibrateWithPattern([duration]);\n    }\n    async selectionStart() {\n        this.selectionStarted = true;\n    }\n    async selectionChanged() {\n        if (this.selectionStarted) {\n            this.vibrateWithPattern([70]);\n        }\n    }\n    async selectionEnd() {\n        this.selectionStarted = false;\n    }\n    patternForImpact(style = ImpactStyle.Heavy) {\n        if (style === ImpactStyle.Medium) {\n            return [43];\n        }\n        else if (style === ImpactStyle.Light) {\n            return [20];\n        }\n        return [61];\n    }\n    patternForNotification(type = NotificationType.Success) {\n        if (type === NotificationType.Warning) {\n            return [30, 40, 30, 50, 60];\n        }\n        else if (type === NotificationType.Error) {\n            return [27, 45, 50];\n        }\n        return [35, 65, 21];\n    }\n    vibrateWithPattern(pattern) {\n        if (navigator.vibrate) {\n            navigator.vibrate(pattern);\n        }\n        else {\n            throw this.unavailable('Browser does not support the vibrate API');\n        }\n    }\n}\n//# sourceMappingURL=web.js.map"], "names": ["ImpactStyle", "NotificationType", "registerPlugin", "WebPlugin"], "mappings": ";;;AAAWA;IACX,CAAC,UAAU,WAAW,EAAE;IACxB;IACA;IACA;IACA;IACA;IACA,IAAI,WAAW,CAAC,OAAO,CAAC,GAAG,OAAO;IAClC;IACA;IACA;IACA;IACA;IACA,IAAI,WAAW,CAAC,QAAQ,CAAC,GAAG,QAAQ;IACpC;IACA;IACA;IACA;IACA;IACA,IAAI,WAAW,CAAC,OAAO,CAAC,GAAG,OAAO;IAClC,CAAC,EAAEA,mBAAW,KAAKA,mBAAW,GAAG,EAAE,CAAC,CAAC;AAC1BC;IACX,CAAC,UAAU,gBAAgB,EAAE;IAC7B;IACA;IACA;IACA;IACA;IACA,IAAI,gBAAgB,CAAC,SAAS,CAAC,GAAG,SAAS;IAC3C;IACA;IACA;IACA;IACA;IACA,IAAI,gBAAgB,CAAC,SAAS,CAAC,GAAG,SAAS;IAC3C;IACA;IACA;IACA;IACA;IACA,IAAI,gBAAgB,CAAC,OAAO,CAAC,GAAG,OAAO;IACvC,CAAC,EAAEA,wBAAgB,KAAKA,wBAAgB,GAAG,EAAE,CAAC,CAAC;;ACxC1C,UAAC,OAAO,GAAGC,mBAAc,CAAC,SAAS,EAAE;IAC1C,IAAI,GAAG,EAAE,MAAM,mDAAe,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,UAAU,EAAE,CAAC;IAC5D,CAAC;;ICDM,MAAM,UAAU,SAASC,cAAS,CAAC;IAC1C,IAAI,WAAW,GAAG;IAClB,QAAQ,KAAK,CAAC,GAAG,SAAS,CAAC;IAC3B,QAAQ,IAAI,CAAC,gBAAgB,GAAG,KAAK;IACrC;IACA,IAAI,MAAM,MAAM,CAAC,OAAO,EAAE;IAC1B,QAAQ,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,MAAM,GAAG,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC;IAC9G,QAAQ,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;IACxC;IACA,IAAI,MAAM,YAAY,CAAC,OAAO,EAAE;IAChC,QAAQ,MAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,MAAM,GAAG,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IACnH,QAAQ,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;IACxC;IACA,IAAI,MAAM,OAAO,CAAC,OAAO,EAAE;IAC3B,QAAQ,MAAM,QAAQ,GAAG,CAAC,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,MAAM,GAAG,MAAM,GAAG,OAAO,CAAC,QAAQ,KAAK,GAAG;IACpG,QAAQ,IAAI,CAAC,kBAAkB,CAAC,CAAC,QAAQ,CAAC,CAAC;IAC3C;IACA,IAAI,MAAM,cAAc,GAAG;IAC3B,QAAQ,IAAI,CAAC,gBAAgB,GAAG,IAAI;IACpC;IACA,IAAI,MAAM,gBAAgB,GAAG;IAC7B,QAAQ,IAAI,IAAI,CAAC,gBAAgB,EAAE;IACnC,YAAY,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,CAAC,CAAC;IACzC;IACA;IACA,IAAI,MAAM,YAAY,GAAG;IACzB,QAAQ,IAAI,CAAC,gBAAgB,GAAG,KAAK;IACrC;IACA,IAAI,gBAAgB,CAAC,KAAK,GAAGH,mBAAW,CAAC,KAAK,EAAE;IAChD,QAAQ,IAAI,KAAK,KAAKA,mBAAW,CAAC,MAAM,EAAE;IAC1C,YAAY,OAAO,CAAC,EAAE,CAAC;IACvB;IACA,aAAa,IAAI,KAAK,KAAKA,mBAAW,CAAC,KAAK,EAAE;IAC9C,YAAY,OAAO,CAAC,EAAE,CAAC;IACvB;IACA,QAAQ,OAAO,CAAC,EAAE,CAAC;IACnB;IACA,IAAI,sBAAsB,CAAC,IAAI,GAAGC,wBAAgB,CAAC,OAAO,EAAE;IAC5D,QAAQ,IAAI,IAAI,KAAKA,wBAAgB,CAAC,OAAO,EAAE;IAC/C,YAAY,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IACvC;IACA,aAAa,IAAI,IAAI,KAAKA,wBAAgB,CAAC,KAAK,EAAE;IAClD,YAAY,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAC/B;IACA,QAAQ,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAC3B;IACA,IAAI,kBAAkB,CAAC,OAAO,EAAE;IAChC,QAAQ,IAAI,SAAS,CAAC,OAAO,EAAE;IAC/B,YAAY,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC;IACtC;IACA,aAAa;IACb,YAAY,MAAM,IAAI,CAAC,WAAW,CAAC,0CAA0C,CAAC;IAC9E;IACA;IACA;;;;;;;;;;;;;;;"}