{"version": 3, "file": "plugin.js", "sources": ["esm/index.js", "esm/web.js"], "sourcesContent": ["import { registerPlugin } from '@capacitor/core';\nconst Share = registerPlugin('Share', {\n    web: () => import('./web').then(m => new m.ShareWeb()),\n});\nexport * from './definitions';\nexport { Share };\n//# sourceMappingURL=index.js.map", "import { WebPlugin } from '@capacitor/core';\nexport class ShareWeb extends WebPlugin {\n    async canShare() {\n        if (typeof navigator === 'undefined' || !navigator.share) {\n            return { value: false };\n        }\n        else {\n            return { value: true };\n        }\n    }\n    async share(options) {\n        if (typeof navigator === 'undefined' || !navigator.share) {\n            throw this.unavailable('Share API not available in this browser');\n        }\n        await navigator.share({\n            title: options.title,\n            text: options.text,\n            url: options.url,\n        });\n        return {};\n    }\n}\n//# sourceMappingURL=web.js.map"], "names": ["registerPlugin", "WebPlugin"], "mappings": ";;;AACK,UAAC,KAAK,GAAGA,mBAAc,CAAC,OAAO,EAAE;IACtC,IAAI,GAAG,EAAE,MAAM,mDAAe,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC;IAC1D,CAAC;;ICFM,MAAM,QAAQ,SAASC,cAAS,CAAC;IACxC,IAAI,MAAM,QAAQ,GAAG;IACrB,QAAQ,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;IAClE,YAAY,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE;IACnC;IACA,aAAa;IACb,YAAY,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE;IAClC;IACA;IACA,IAAI,MAAM,KAAK,CAAC,OAAO,EAAE;IACzB,QAAQ,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;IAClE,YAAY,MAAM,IAAI,CAAC,WAAW,CAAC,yCAAyC,CAAC;IAC7E;IACA,QAAQ,MAAM,SAAS,CAAC,KAAK,CAAC;IAC9B,YAAY,KAAK,EAAE,OAAO,CAAC,KAAK;IAChC,YAAY,IAAI,EAAE,OAAO,CAAC,IAAI;IAC9B,YAAY,GAAG,EAAE,OAAO,CAAC,GAAG;IAC5B,SAAS,CAAC;IACV,QAAQ,OAAO,EAAE;IACjB;IACA;;;;;;;;;;;;;;;"}