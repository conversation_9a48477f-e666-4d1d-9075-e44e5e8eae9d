# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [3.1.7](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-fs@3.1.6...@ionic/utils-fs@3.1.7) (2023-03-29)

**Note:** Version bump only for package @ionic/utils-fs





## [3.1.6](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-fs@3.1.5...@ionic/utils-fs@3.1.6) (2022-06-16)

**Note:** Version bump only for package @ionic/utils-fs





## [3.1.5](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-fs@3.1.4...@ionic/utils-fs@3.1.5) (2020-08-28)

**Note:** Version bump only for package @ionic/utils-fs





## [3.1.4](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-fs@3.1.3...@ionic/utils-fs@3.1.4) (2020-08-25)

**Note:** Version bump only for package @ionic/utils-fs





## [3.1.3](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-fs@3.1.2...@ionic/utils-fs@3.1.3) (2020-05-12)


### Bug Fixes

* pin tslib to avoid "Cannot set property pathExists" error ([689e1f0](https://github.com/ionic-team/ionic-cli/commit/689e1f038b907356ef855a067a76d4822e7072a8))





## [3.1.2](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-fs@3.1.1...@ionic/utils-fs@3.1.2) (2020-05-06)

**Note:** Version bump only for package @ionic/utils-fs





## [3.1.1](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-fs@3.1.0...@ionic/utils-fs@3.1.1) (2020-03-03)

**Note:** Version bump only for package @ionic/utils-fs





# 3.1.0 (2020-02-11)


### Features

* **start:** add new list starter option ([#4315](https://github.com/ionic-team/ionic-cli/issues/4315)) ([1df44c1](https://github.com/ionic-team/ionic-cli/commit/1df44c1591f37b89f2b672857740edd6cb2aea67))





## [3.0.2](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-fs@3.0.1...@ionic/utils-fs@3.0.2) (2020-02-10)

**Note:** Version bump only for package @ionic/utils-fs





## [3.0.1](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-fs@3.0.0...@ionic/utils-fs@3.0.1) (2020-02-03)

**Note:** Version bump only for package @ionic/utils-fs





# [3.0.0](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-fs@2.0.9...@ionic/utils-fs@3.0.0) (2020-01-25)


### chore

* require Node 10 ([5a47874](https://github.com/ionic-team/ionic-cli/commit/5a478746c074207b6dc96aa8771f04a606deb1ef))


### BREAKING CHANGES

* A minimum of Node.js 10.3.0 is required.





## [2.0.9](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-fs@2.0.8...@ionic/utils-fs@2.0.9) (2019-12-05)

**Note:** Version bump only for package @ionic/utils-fs





## [2.0.8](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-fs@2.0.7...@ionic/utils-fs@2.0.8) (2019-10-14)


### Bug Fixes

* **fileToString:** handle ENOTDIR errors ([fab07d8](https://github.com/ionic-team/ionic-cli/commit/fab07d8c6e3d6756d633ada4c81165d11c70eb8f))





## [2.0.7](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-fs@2.0.6...@ionic/utils-fs@2.0.7) (2019-09-18)

**Note:** Version bump only for package @ionic/utils-fs





## [2.0.6](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-fs@2.0.5...@ionic/utils-fs@2.0.6) (2019-08-23)

**Note:** Version bump only for package @ionic/utils-fs





## [2.0.5](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-fs@2.0.4...@ionic/utils-fs@2.0.5) (2019-08-14)

**Note:** Version bump only for package @ionic/utils-fs





## [2.0.4](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-fs@2.0.3...@ionic/utils-fs@2.0.4) (2019-08-07)

**Note:** Version bump only for package @ionic/utils-fs





## [2.0.3](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-fs@2.0.2...@ionic/utils-fs@2.0.3) (2019-06-28)

**Note:** Version bump only for package @ionic/utils-fs





## [2.0.2](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-fs@2.0.1...@ionic/utils-fs@2.0.2) (2019-06-10)

**Note:** Version bump only for package @ionic/utils-fs





## [2.0.1](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-fs@2.0.0...@ionic/utils-fs@2.0.1) (2019-06-05)

**Note:** Version bump only for package @ionic/utils-fs





# [2.0.0](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-fs@1.2.0...@ionic/utils-fs@2.0.0) (2019-05-29)


### chore

* require Node 8 ([5670e68](https://github.com/ionic-team/ionic-cli/commit/5670e68))


### BREAKING CHANGES

* A minimum of Node.js 8.9.4 is required.





<a name="1.2.0"></a>
# [1.2.0](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-fs@1.1.1...@ionic/utils-fs@1.2.0) (2019-03-06)


### Features

* **fs:** add `isExecutableFile` utility ([53d9626](https://github.com/ionic-team/ionic-cli/commit/53d9626))
* **fs:** add `pathReadable`, `pathWritable`, and `pathExecutable` ([5412791](https://github.com/ionic-team/ionic-cli/commit/5412791))




<a name="1.1.1"></a>
## [1.1.1](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-fs@1.1.0...@ionic/utils-fs@1.1.1) (2019-02-27)




**Note:** Version bump only for package @ionic/utils-fs

<a name="1.1.0"></a>
# [1.1.0](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-fs@1.0.0...@ionic/utils-fs@1.1.0) (2019-02-15)


### Bug Fixes

* **walk:** stat symlinks themselves while walking ([92f2cee](https://github.com/ionic-team/ionic-cli/commit/92f2cee))


### Features

* **fs:** `onError` option for `readdirp` and `getFileTree` ([90bcfcd](https://github.com/ionic-team/ionic-cli/commit/90bcfcd))
* **fs:** `onFileNode` and `onDirectoryNode` options for `getFileTree` ([1858e96](https://github.com/ionic-team/ionic-cli/commit/1858e96))
* **fs:** add `getFileTree` for creating file tree object structures ([c14b46b](https://github.com/ionic-team/ionic-cli/commit/c14b46b))




<a name="1.0.0"></a>
# [1.0.0](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-fs@0.0.7...@ionic/utils-fs@1.0.0) (2019-01-08)


### Bug Fixes

* rimraf is a full dependency ([962ad94](https://github.com/ionic-team/ionic-cli/commit/962ad94))


### Features

* switch to fs-extra ([c17d8d8](https://github.com/ionic-team/ionic-cli/commit/c17d8d8))


### BREAKING CHANGES

* `readDir`, `readDirp`, `readDirSafe` were renamed to be
consistent with fs and fs-extra. `readJsonFile` and `writeJsonFile` have
been renamed to `readJson` and `writeJson` and use the fs-extra version
directly. `isDir` was removed (use stat). `copyFile` and `copyDirectory`
were removed (use `copy`). `removeDirectory` was removed (use `remove`).




<a name="0.0.7"></a>
## [0.0.7](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-fs@0.0.6...@ionic/utils-fs@0.0.7) (2019-01-07)




**Note:** Version bump only for package @ionic/utils-fs

<a name="0.0.6"></a>
## [0.0.6](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-fs@0.0.5...@ionic/utils-fs@0.0.6) (2018-12-19)




**Note:** Version bump only for package @ionic/utils-fs

<a name="0.0.5"></a>
## [0.0.5](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-fs@0.0.4...@ionic/utils-fs@0.0.5) (2018-11-20)




**Note:** Version bump only for package @ionic/utils-fs

<a name="0.0.4"></a>
## [0.0.4](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-fs@0.0.3...@ionic/utils-fs@0.0.4) (2018-10-31)




**Note:** Version bump only for package @ionic/utils-fs

<a name="0.0.3"></a>
## [0.0.3](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-fs@0.0.2...@ionic/utils-fs@0.0.3) (2018-10-05)




**Note:** Version bump only for package @ionic/utils-fs

<a name="0.0.2"></a>
## [0.0.2](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-fs@0.0.1...@ionic/utils-fs@0.0.2) (2018-10-03)




**Note:** Version bump only for package @ionic/utils-fs

<a name="0.0.1"></a>
## 0.0.1 (2018-09-05)




**Note:** Version bump only for package @ionic/utils-fs

# Change Log
