{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_f58e8655._.js", "server/edge/chunks/[root-of-the-server]__c6c91fdf._.js", "server/edge/chunks/edge-wrapper_c6630286.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "3bMorcNST1nn7TlHZNKY9DXdKc+0VsLLH8M8jnlcKQ0=", "__NEXT_PREVIEW_MODE_ID": "7bc51e85d4ef98e4e4632c0056cd9dd7", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "c89831a047e20e0a6dd5d8a701c51d04561daca789440eae50eb5ba9c1300140", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0d8e5d79f11fb9de3a9d8de59d3abc1c054a2499a5b9a6f3e9e514d2444eef52"}}}, "instrumentation": null, "functions": {}}