{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_f58e8655._.js", "server/edge/chunks/[root-of-the-server]__c6c91fdf._.js", "server/edge/chunks/edge-wrapper_c6630286.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Lzxq+LF28sVcjYZHZyFkzVYZUvwisl0bINLEPMHycW8=", "__NEXT_PREVIEW_MODE_ID": "6f45820d2533f403fea9768530ab1656", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "52987675ceb38192e0f7c84a05c0352d54b0f51d1ea5610173b97a3624e8f58d", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "6dffac18be53d7dc30f1fd7733fac87b548a21417f0c0c1ff64bb625e0a8a207"}}}, "instrumentation": null, "functions": {}}