{"version": 3, "file": "plugin.js", "sources": ["esm/index.js", "esm/web.js"], "sourcesContent": ["import { registerPlugin } from '@capacitor/core';\nconst Device = registerPlugin('Device', {\n    web: () => import('./web').then(m => new m.DeviceWeb()),\n});\nexport * from './definitions';\nexport { Device };\n//# sourceMappingURL=index.js.map", "import { WebPlugin } from '@capacitor/core';\nexport class <PERSON>ceWeb extends WebPlugin {\n    async getId() {\n        return {\n            identifier: this.getUid(),\n        };\n    }\n    async getInfo() {\n        if (typeof navigator === 'undefined' || !navigator.userAgent) {\n            throw this.unavailable('Device API not available in this browser');\n        }\n        const ua = navigator.userAgent;\n        const uaFields = this.parseUa(ua);\n        return {\n            model: uaFields.model,\n            platform: 'web',\n            operatingSystem: uaFields.operatingSystem,\n            osVersion: uaFields.osVersion,\n            manufacturer: navigator.vendor,\n            isVirtual: false,\n            webViewVersion: uaFields.browserVersion,\n        };\n    }\n    async getBatteryInfo() {\n        if (typeof navigator === 'undefined' || !navigator.getBattery) {\n            throw this.unavailable('Device API not available in this browser');\n        }\n        let battery = {};\n        try {\n            battery = await navigator.getBattery();\n        }\n        catch (e) {\n            // Let it fail, we don't care\n        }\n        return {\n            batteryLevel: battery.level,\n            isCharging: battery.charging,\n        };\n    }\n    async getLanguageCode() {\n        return {\n            value: navigator.language.split('-')[0].toLowerCase(),\n        };\n    }\n    async getLanguageTag() {\n        return {\n            value: navigator.language,\n        };\n    }\n    parseUa(ua) {\n        const uaFields = {};\n        const start = ua.indexOf('(') + 1;\n        let end = ua.indexOf(') AppleWebKit');\n        if (ua.indexOf(') Gecko') !== -1) {\n            end = ua.indexOf(') Gecko');\n        }\n        const fields = ua.substring(start, end);\n        if (ua.indexOf('Android') !== -1) {\n            const tmpFields = fields.replace('; wv', '').split('; ').pop();\n            if (tmpFields) {\n                uaFields.model = tmpFields.split(' Build')[0];\n            }\n            uaFields.osVersion = fields.split('; ')[1];\n        }\n        else {\n            uaFields.model = fields.split('; ')[0];\n            if (typeof navigator !== 'undefined' && navigator.oscpu) {\n                uaFields.osVersion = navigator.oscpu;\n            }\n            else {\n                if (ua.indexOf('Windows') !== -1) {\n                    uaFields.osVersion = fields;\n                }\n                else {\n                    const tmpFields = fields.split('; ').pop();\n                    if (tmpFields) {\n                        const lastParts = tmpFields\n                            .replace(' like Mac OS X', '')\n                            .split(' ');\n                        uaFields.osVersion = lastParts[lastParts.length - 1].replace(/_/g, '.');\n                    }\n                }\n            }\n        }\n        if (/android/i.test(ua)) {\n            uaFields.operatingSystem = 'android';\n        }\n        else if (/iPad|iPhone|iPod/.test(ua) && !window.MSStream) {\n            uaFields.operatingSystem = 'ios';\n        }\n        else if (/Win/.test(ua)) {\n            uaFields.operatingSystem = 'windows';\n        }\n        else if (/Mac/i.test(ua)) {\n            uaFields.operatingSystem = 'mac';\n        }\n        else {\n            uaFields.operatingSystem = 'unknown';\n        }\n        // Check for browsers based on non-standard javascript apis, only not user agent\n        const isSafari = !!window.ApplePaySession;\n        const isChrome = !!window.chrome;\n        const isFirefox = /Firefox/.test(ua);\n        const isEdge = /Edg/.test(ua);\n        const isFirefoxIOS = /FxiOS/.test(ua);\n        const isChromeIOS = /CriOS/.test(ua);\n        const isEdgeIOS = /EdgiOS/.test(ua);\n        // FF and Edge User Agents both end with \"/MAJOR.MINOR\"\n        if (isSafari ||\n            (isChrome && !isEdge) ||\n            isFirefoxIOS ||\n            isChromeIOS ||\n            isEdgeIOS) {\n            // Safari version comes as     \"... Version/MAJOR.MINOR ...\"\n            // Chrome version comes as     \"... Chrome/MAJOR.MINOR ...\"\n            // FirefoxIOS version comes as \"... FxiOS/MAJOR.MINOR ...\"\n            // ChromeIOS version comes as  \"... CriOS/MAJOR.MINOR ...\"\n            let searchWord;\n            if (isFirefoxIOS) {\n                searchWord = 'FxiOS';\n            }\n            else if (isChromeIOS) {\n                searchWord = 'CriOS';\n            }\n            else if (isEdgeIOS) {\n                searchWord = 'EdgiOS';\n            }\n            else if (isSafari) {\n                searchWord = 'Version';\n            }\n            else {\n                searchWord = 'Chrome';\n            }\n            const words = ua.split(' ');\n            for (const word of words) {\n                if (word.includes(searchWord)) {\n                    const version = word.split('/')[1];\n                    uaFields.browserVersion = version;\n                }\n            }\n        }\n        else if (isFirefox || isEdge) {\n            const reverseUA = ua.split('').reverse().join('');\n            const reverseVersion = reverseUA.split('/')[0];\n            const version = reverseVersion.split('').reverse().join('');\n            uaFields.browserVersion = version;\n        }\n        return uaFields;\n    }\n    getUid() {\n        if (typeof window !== 'undefined' && window.localStorage) {\n            let uid = window.localStorage.getItem('_capuid');\n            if (uid) {\n                return uid;\n            }\n            uid = this.uuid4();\n            window.localStorage.setItem('_capuid', uid);\n            return uid;\n        }\n        return this.uuid4();\n    }\n    uuid4() {\n        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n            const r = (Math.random() * 16) | 0, v = c === 'x' ? r : (r & 0x3) | 0x8;\n            return v.toString(16);\n        });\n    }\n}\n//# sourceMappingURL=web.js.map"], "names": ["registerPlugin", "WebPlugin"], "mappings": ";;;AACK,UAAC,MAAM,GAAGA,mBAAc,CAAC,QAAQ,EAAE;IACxC,IAAI,GAAG,EAAE,MAAM,mDAAe,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,SAAS,EAAE,CAAC;IAC3D,CAAC;;ICFM,MAAM,SAAS,SAASC,cAAS,CAAC;IACzC,IAAI,MAAM,KAAK,GAAG;IAClB,QAAQ,OAAO;IACf,YAAY,UAAU,EAAE,IAAI,CAAC,MAAM,EAAE;IACrC,SAAS;IACT;IACA,IAAI,MAAM,OAAO,GAAG;IACpB,QAAQ,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;IACtE,YAAY,MAAM,IAAI,CAAC,WAAW,CAAC,0CAA0C,CAAC;IAC9E;IACA,QAAQ,MAAM,EAAE,GAAG,SAAS,CAAC,SAAS;IACtC,QAAQ,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;IACzC,QAAQ,OAAO;IACf,YAAY,KAAK,EAAE,QAAQ,CAAC,KAAK;IACjC,YAAY,QAAQ,EAAE,KAAK;IAC3B,YAAY,eAAe,EAAE,QAAQ,CAAC,eAAe;IACrD,YAAY,SAAS,EAAE,QAAQ,CAAC,SAAS;IACzC,YAAY,YAAY,EAAE,SAAS,CAAC,MAAM;IAC1C,YAAY,SAAS,EAAE,KAAK;IAC5B,YAAY,cAAc,EAAE,QAAQ,CAAC,cAAc;IACnD,SAAS;IACT;IACA,IAAI,MAAM,cAAc,GAAG;IAC3B,QAAQ,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE;IACvE,YAAY,MAAM,IAAI,CAAC,WAAW,CAAC,0CAA0C,CAAC;IAC9E;IACA,QAAQ,IAAI,OAAO,GAAG,EAAE;IACxB,QAAQ,IAAI;IACZ,YAAY,OAAO,GAAG,MAAM,SAAS,CAAC,UAAU,EAAE;IAClD;IACA,QAAQ,OAAO,CAAC,EAAE;IAClB;IACA;IACA,QAAQ,OAAO;IACf,YAAY,YAAY,EAAE,OAAO,CAAC,KAAK;IACvC,YAAY,UAAU,EAAE,OAAO,CAAC,QAAQ;IACxC,SAAS;IACT;IACA,IAAI,MAAM,eAAe,GAAG;IAC5B,QAAQ,OAAO;IACf,YAAY,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE;IACjE,SAAS;IACT;IACA,IAAI,MAAM,cAAc,GAAG;IAC3B,QAAQ,OAAO;IACf,YAAY,KAAK,EAAE,SAAS,CAAC,QAAQ;IACrC,SAAS;IACT;IACA,IAAI,OAAO,CAAC,EAAE,EAAE;IAChB,QAAQ,MAAM,QAAQ,GAAG,EAAE;IAC3B,QAAQ,MAAM,KAAK,GAAG,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC;IACzC,QAAQ,IAAI,GAAG,GAAG,EAAE,CAAC,OAAO,CAAC,eAAe,CAAC;IAC7C,QAAQ,IAAI,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,EAAE,EAAE;IAC1C,YAAY,GAAG,GAAG,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC;IACvC;IACA,QAAQ,MAAM,MAAM,GAAG,EAAE,CAAC,SAAS,CAAC,KAAK,EAAE,GAAG,CAAC;IAC/C,QAAQ,IAAI,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,EAAE,EAAE;IAC1C,YAAY,MAAM,SAAS,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE;IAC1E,YAAY,IAAI,SAAS,EAAE;IAC3B,gBAAgB,QAAQ,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC7D;IACA,YAAY,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACtD;IACA,aAAa;IACb,YAAY,QAAQ,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClD,YAAY,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,KAAK,EAAE;IACrE,gBAAgB,QAAQ,CAAC,SAAS,GAAG,SAAS,CAAC,KAAK;IACpD;IACA,iBAAiB;IACjB,gBAAgB,IAAI,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,EAAE,EAAE;IAClD,oBAAoB,QAAQ,CAAC,SAAS,GAAG,MAAM;IAC/C;IACA,qBAAqB;IACrB,oBAAoB,MAAM,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE;IAC9D,oBAAoB,IAAI,SAAS,EAAE;IACnC,wBAAwB,MAAM,SAAS,GAAG;IAC1C,6BAA6B,OAAO,CAAC,gBAAgB,EAAE,EAAE;IACzD,6BAA6B,KAAK,CAAC,GAAG,CAAC;IACvC,wBAAwB,QAAQ,CAAC,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;IAC/F;IACA;IACA;IACA;IACA,QAAQ,IAAI,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;IACjC,YAAY,QAAQ,CAAC,eAAe,GAAG,SAAS;IAChD;IACA,aAAa,IAAI,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;IAClE,YAAY,QAAQ,CAAC,eAAe,GAAG,KAAK;IAC5C;IACA,aAAa,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;IACjC,YAAY,QAAQ,CAAC,eAAe,GAAG,SAAS;IAChD;IACA,aAAa,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;IAClC,YAAY,QAAQ,CAAC,eAAe,GAAG,KAAK;IAC5C;IACA,aAAa;IACb,YAAY,QAAQ,CAAC,eAAe,GAAG,SAAS;IAChD;IACA;IACA,QAAQ,MAAM,QAAQ,GAAG,CAAC,CAAC,MAAM,CAAC,eAAe;IACjD,QAAQ,MAAM,QAAQ,GAAG,CAAC,CAAC,MAAM,CAAC,MAAM;IACxC,QAAQ,MAAM,SAAS,GAAG,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;IAC5C,QAAQ,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;IACrC,QAAQ,MAAM,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;IAC7C,QAAQ,MAAM,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;IAC5C,QAAQ,MAAM,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;IAC3C;IACA,QAAQ,IAAI,QAAQ;IACpB,aAAa,QAAQ,IAAI,CAAC,MAAM,CAAC;IACjC,YAAY,YAAY;IACxB,YAAY,WAAW;IACvB,YAAY,SAAS,EAAE;IACvB;IACA;IACA;IACA;IACA,YAAY,IAAI,UAAU;IAC1B,YAAY,IAAI,YAAY,EAAE;IAC9B,gBAAgB,UAAU,GAAG,OAAO;IACpC;IACA,iBAAiB,IAAI,WAAW,EAAE;IAClC,gBAAgB,UAAU,GAAG,OAAO;IACpC;IACA,iBAAiB,IAAI,SAAS,EAAE;IAChC,gBAAgB,UAAU,GAAG,QAAQ;IACrC;IACA,iBAAiB,IAAI,QAAQ,EAAE;IAC/B,gBAAgB,UAAU,GAAG,SAAS;IACtC;IACA,iBAAiB;IACjB,gBAAgB,UAAU,GAAG,QAAQ;IACrC;IACA,YAAY,MAAM,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC;IACvC,YAAY,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;IACtC,gBAAgB,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE;IAC/C,oBAAoB,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACtD,oBAAoB,QAAQ,CAAC,cAAc,GAAG,OAAO;IACrD;IACA;IACA;IACA,aAAa,IAAI,SAAS,IAAI,MAAM,EAAE;IACtC,YAAY,MAAM,SAAS,GAAG,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;IAC7D,YAAY,MAAM,cAAc,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC1D,YAAY,MAAM,OAAO,GAAG,cAAc,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;IACvE,YAAY,QAAQ,CAAC,cAAc,GAAG,OAAO;IAC7C;IACA,QAAQ,OAAO,QAAQ;IACvB;IACA,IAAI,MAAM,GAAG;IACb,QAAQ,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,YAAY,EAAE;IAClE,YAAY,IAAI,GAAG,GAAG,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC;IAC5D,YAAY,IAAI,GAAG,EAAE;IACrB,gBAAgB,OAAO,GAAG;IAC1B;IACA,YAAY,GAAG,GAAG,IAAI,CAAC,KAAK,EAAE;IAC9B,YAAY,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;IACvD,YAAY,OAAO,GAAG;IACtB;IACA,QAAQ,OAAO,IAAI,CAAC,KAAK,EAAE;IAC3B;IACA,IAAI,KAAK,GAAG;IACZ,QAAQ,OAAO,sCAAsC,CAAC,OAAO,CAAC,OAAO,EAAE,UAAU,CAAC,EAAE;IACpF,YAAY,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG;IACnF,YAAY,OAAO,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC;IACjC,SAAS,CAAC;IACV;IACA;;;;;;;;;;;;;;;"}