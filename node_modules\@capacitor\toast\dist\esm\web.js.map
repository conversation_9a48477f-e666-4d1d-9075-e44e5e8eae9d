{"version": 3, "file": "web.js", "sourceRoot": "", "sources": ["../../src/web.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAI5C,MAAM,OAAO,QAAS,SAAQ,SAAS;IACrC,KAAK,CAAC,IAAI,CAAC,OAAoB;QAC7B,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;YACnC,IAAI,QAAQ,GAAG,IAAI,CAAC;YACpB,IAAI,OAAO,CAAC,QAAQ,EAAE;gBACpB,QAAQ,GAAG,OAAO,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;aACtD;YACD,MAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,WAAW,CAAQ,CAAC;YACzD,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;YAC1B,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;YAC7B,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;SAClC;IACH,CAAC;CACF", "sourcesContent": ["import { WebPlugin } from '@capacitor/core';\n\nimport type { ToastPlugin, ShowOptions } from './definitions';\n\nexport class ToastWeb extends WebPlugin implements ToastPlugin {\n  async show(options: ShowOptions): Promise<void> {\n    if (typeof document !== 'undefined') {\n      let duration = 2000;\n      if (options.duration) {\n        duration = options.duration === 'long' ? 3500 : 2000;\n      }\n      const toast = document.createElement('pwa-toast') as any;\n      toast.duration = duration;\n      toast.message = options.text;\n      document.body.appendChild(toast);\n    }\n  }\n}\n"]}