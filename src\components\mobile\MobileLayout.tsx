'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useCapacitor, isMobile } from '@/lib/capacitor'
import { 
  HomeIcon, 
  CheckSquareIcon, 
  DollarSignIcon, 
  ShoppingCartIcon, 
  MessageSquareIcon, 
  ChefHatIcon,
  CalendarIcon,
  MenuIcon,
  XIcon,
  WifiOffIcon
} from 'lucide-react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'

interface MobileLayoutProps {
  children: React.ReactNode
}

const mobileNavItems = [
  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },
  { name: 'Tasks', href: '/tasks', icon: CheckSquareIcon },
  { name: 'Budget', href: '/budget', icon: DollarSignIcon },
  { name: 'Shopping', href: '/shopping', icon: ShoppingCartIcon },
  { name: 'Chat', href: '/chat', icon: MessageSquareIcon },
  { name: 'Recipes', href: '/recipes', icon: ChefHatIcon },
  { name: 'Meals', href: '/meal-plans', icon: CalendarIcon },
]

export default function MobileLayout({ children }: MobileLayoutProps) {
  const [showMobileMenu, setShowMobileMenu] = useState(false)
  const { isNative, networkStatus, hapticImpact } = useCapacitor()
  const pathname = usePathname()
  const mobile = isMobile()

  // Don't render mobile layout if not on mobile
  if (!mobile) {
    return <>{children}</>
  }

  const handleNavClick = async (href: string) => {
    if (isNative) {
      await hapticImpact()
    }
    setShowMobileMenu(false)
  }

  const currentNavItem = mobileNavItems.find(item => pathname.startsWith(item.href))

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Status Bar Spacer for iOS */}
      {isNative && (
        <div className="h-safe-top bg-blue-600"></div>
      )}

      {/* Top Header */}
      <header className="bg-blue-600 text-white shadow-lg">
        <div className="flex items-center justify-between px-4 py-3">
          <div className="flex items-center space-x-3">
            <button
              onClick={() => {
                setShowMobileMenu(!showMobileMenu)
                if (isNative) hapticImpact()
              }}
              className="p-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              {showMobileMenu ? (
                <XIcon className="h-6 w-6" />
              ) : (
                <MenuIcon className="h-6 w-6" />
              )}
            </button>
            <h1 className="text-lg font-semibold">
              {currentNavItem?.name || 'LifeManager'}
            </h1>
          </div>

          {/* Network Status Indicator */}
          {!networkStatus.connected && (
            <div className="flex items-center space-x-1 text-yellow-200">
              <WifiOffIcon className="h-4 w-4" />
              <span className="text-xs">Offline</span>
            </div>
          )}
        </div>
      </header>

      {/* Mobile Menu Overlay */}
      <AnimatePresence>
        {showMobileMenu && (
          <>
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black bg-opacity-50 z-40"
              onClick={() => setShowMobileMenu(false)}
            />

            {/* Menu Panel */}
            <motion.div
              initial={{ x: '-100%' }}
              animate={{ x: 0 }}
              exit={{ x: '-100%' }}
              transition={{ type: 'spring', damping: 25, stiffness: 200 }}
              className="fixed left-0 top-0 bottom-0 w-80 bg-white shadow-xl z-50 flex flex-col"
            >
              {/* Menu Header */}
              <div className="bg-blue-600 text-white p-4">
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-bold">LifeManager</h2>
                  <button
                    onClick={() => setShowMobileMenu(false)}
                    className="p-2 rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    <XIcon className="h-5 w-5" />
                  </button>
                </div>
              </div>

              {/* Navigation Items */}
              <nav className="flex-1 py-4">
                {mobileNavItems.map((item) => {
                  const isActive = pathname.startsWith(item.href)
                  return (
                    <Link
                      key={item.name}
                      href={item.href}
                      onClick={() => handleNavClick(item.href)}
                      className={`flex items-center space-x-3 px-6 py-4 text-gray-700 hover:bg-gray-100 transition-colors ${
                        isActive ? 'bg-blue-50 text-blue-600 border-r-4 border-blue-600' : ''
                      }`}
                    >
                      <item.icon className={`h-6 w-6 ${isActive ? 'text-blue-600' : 'text-gray-500'}`} />
                      <span className="font-medium">{item.name}</span>
                    </Link>
                  )
                })}
              </nav>

              {/* Menu Footer */}
              <div className="p-4 border-t border-gray-200">
                <div className="text-xs text-gray-500 text-center">
                  {isNative ? 'Mobile App' : 'Web App'} • v1.0.0
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>

      {/* Main Content */}
      <main className="flex-1 overflow-auto">
        <div className="p-4">
          {children}
        </div>
      </main>

      {/* Bottom Navigation */}
      <nav className="bg-white border-t border-gray-200 px-2 py-1 safe-bottom">
        <div className="flex items-center justify-around">
          {mobileNavItems.slice(0, 5).map((item) => {
            const isActive = pathname.startsWith(item.href)
            return (
              <Link
                key={item.name}
                href={item.href}
                onClick={() => handleNavClick(item.href)}
                className={`flex flex-col items-center space-y-1 px-3 py-2 rounded-lg transition-colors ${
                  isActive 
                    ? 'text-blue-600 bg-blue-50' 
                    : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                }`}
              >
                <item.icon className="h-5 w-5" />
                <span className="text-xs font-medium">{item.name}</span>
              </Link>
            )
          })}
        </div>
      </nav>

      {/* Safe Area Bottom Spacer for iOS */}
      {isNative && (
        <div className="h-safe-bottom bg-white"></div>
      )}
    </div>
  )
}

// Hook for mobile-specific features
export function useMobileFeatures() {
  const { isNative, hapticImpact, hapticNotification, showToast, share } = useCapacitor()
  const [isOnline, setIsOnline] = useState(true)

  useEffect(() => {
    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  const showSuccessToast = async (message: string) => {
    if (isNative) {
      await hapticNotification('success')
      await showToast(message)
    }
  }

  const showErrorToast = async (message: string) => {
    if (isNative) {
      await hapticNotification('error')
      await showToast(message)
    }
  }

  const shareContent = async (content: { title?: string; text?: string; url?: string }) => {
    await share(content)
  }

  return {
    isNative,
    isOnline,
    hapticImpact,
    showSuccessToast,
    showErrorToast,
    shareContent
  }
}
