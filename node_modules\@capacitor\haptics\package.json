{"name": "@capacitor/haptics", "version": "7.0.1", "description": "The Haptics API provides physical feedback to the user through touch or vibration.", "main": "dist/plugin.cjs.js", "module": "dist/esm/index.js", "types": "dist/esm/index.d.ts", "unpkg": "dist/plugin.js", "files": ["android/src/main/", "android/build.gradle", "dist/", "ios/Sources", "ios/Tests", "Package.swift", "CapacitorHaptics.podspec"], "author": "Ionic <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/ionic-team/capacitor-plugins"}, "bugs": {"url": "https://github.com/ionic-team/capacitor-plugins/issues"}, "keywords": ["capacitor", "plugin", "native"], "scripts": {"verify": "npm run verify:ios && npm run verify:android && npm run verify:web", "verify:ios": "xcodebuild build -scheme CapacitorHaptics -destination generic/platform=iOS", "verify:android": "cd android && ./gradlew clean build test && cd ..", "verify:web": "npm run build", "lint": "npm run eslint && npm run prettier -- --check && npm run swiftlint -- lint", "fmt": "npm run eslint -- --fix && npm run prettier -- --write && npm run swiftlint -- --fix --format", "eslint": "eslint . --ext ts", "prettier": "prettier \"**/*.{css,html,ts,js,java}\"", "swiftlint": "node-swiftlint", "docgen": "docgen --api HapticsPlugin --output-readme README.md --output-json dist/docs.json", "build": "npm run clean && npm run docgen && tsc && rollup -c rollup.config.mjs", "clean": "rimraf ./dist", "watch": "tsc --watch", "prepublishOnly": "npm run build", "publish:cocoapod": "pod trunk push ./CapacitorHaptics.podspec --allow-warnings"}, "devDependencies": {"@capacitor/android": "next", "@capacitor/core": "next", "@capacitor/docgen": "0.2.2", "@capacitor/ios": "next", "@ionic/eslint-config": "^0.4.0", "@ionic/prettier-config": "~1.0.1", "@ionic/swiftlint-config": "^1.1.2", "eslint": "^8.57.0", "prettier": "~2.3.0", "prettier-plugin-java": "~1.0.2", "rimraf": "^6.0.1", "rollup": "^4.26.0", "swiftlint": "^1.0.1", "typescript": "~4.1.5"}, "peerDependencies": {"@capacitor/core": ">=7.0.0"}, "prettier": "@ionic/prettier-config", "swiftlint": "@ionic/swiftlint-config", "eslintConfig": {"extends": "@ionic/eslint-config/recommended"}, "capacitor": {"ios": {"src": "ios"}, "android": {"src": "android"}}, "publishConfig": {"access": "public"}, "gitHead": "927f549a995118acd8e5735835300c4c3cbf3de7"}