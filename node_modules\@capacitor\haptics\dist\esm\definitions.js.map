{"version": 3, "file": "definitions.js", "sourceRoot": "", "sources": ["../../src/definitions.ts"], "names": [], "mappings": "AA2DA,MAAM,CAAN,IAAY,WAqBX;AArBD,WAAY,WAAW;IACrB;;;;OAIG;IACH,8BAAe,CAAA;IAEf;;;;OAIG;IACH,gCAAiB,CAAA;IAEjB;;;;OAIG;IACH,8BAAe,CAAA;AACjB,CAAC,EArBW,WAAW,KAAX,WAAW,QAqBtB;AAcD,MAAM,CAAN,IAAY,gBAqBX;AArBD,WAAY,gBAAgB;IAC1B;;;;OAIG;IACH,uCAAmB,CAAA;IAEnB;;;;OAIG;IACH,uCAAmB,CAAA;IAEnB;;;;OAIG;IACH,mCAAe,CAAA;AACjB,CAAC,EArBW,gBAAgB,KAAhB,gBAAgB,QAqB3B", "sourcesContent": ["export interface HapticsPlugin {\n  /**\n   * Trigger a haptics \"impact\" feedback\n   *\n   * @since 1.0.0\n   */\n  impact(options?: ImpactOptions): Promise<void>;\n\n  /**\n   * Trigger a haptics \"notification\" feedback\n   *\n   * @since 1.0.0\n   */\n  notification(options?: NotificationOptions): Promise<void>;\n\n  /**\n   * Vibrate the device\n   *\n   * @since 1.0.0\n   */\n  vibrate(options?: VibrateOptions): Promise<void>;\n\n  /**\n   * Trigger a selection started haptic hint\n   *\n   * @since 1.0.0\n   */\n  selectionStart(): Promise<void>;\n\n  /**\n   * Trigger a selection changed haptic hint. If a selection was\n   * started already, this will cause the device to provide haptic\n   * feedback\n   *\n   * @since 1.0.0\n   */\n  selectionChanged(): Promise<void>;\n\n  /**\n   * If selectionStart() was called, selectionEnd() ends the selection.\n   * For example, call this when a user has lifted their finger from a control\n   *\n   * @since 1.0.0\n   */\n  selectionEnd(): Promise<void>;\n}\n\nexport interface ImpactOptions {\n  /**\n   * Impact Feedback Style\n   *\n   * The mass of the objects in the collision simulated by a [UIImpactFeedbackGenerator](https://developer.apple.com/documentation/uikit/uiimpactfeedbackstyle) object.\n   *\n   * @default ImpactStyle.Heavy\n   * @since 1.0.0\n   */\n  style: ImpactStyle;\n}\n\nexport enum ImpactStyle {\n  /**\n   * A collision between large, heavy user interface elements\n   *\n   * @since 1.0.0\n   */\n  Heavy = 'HEAVY',\n\n  /**\n   * A collision between moderately sized user interface elements\n   *\n   * @since 1.0.0\n   */\n  Medium = 'MEDIUM',\n\n  /**\n   * A collision between small, light user interface elements\n   *\n   * @since 1.0.0\n   */\n  Light = 'LIGHT',\n}\n\nexport interface NotificationOptions {\n  /**\n   * Notification Feedback Type\n   *\n   * The type of notification feedback generated by a [UINotificationFeedbackGenerator](https://developer.apple.com/documentation/uikit/uinotificationfeedbacktype) object.\n   *\n   * @default NotificationType.SUCCESS\n   * @since 1.0.0\n   */\n  type: NotificationType;\n}\n\nexport enum NotificationType {\n  /**\n   * A notification feedback type indicating that a task has completed successfully\n   *\n   * @since 1.0.0\n   */\n  Success = 'SUCCESS',\n\n  /**\n   * A notification feedback type indicating that a task has produced a warning\n   *\n   * @since 1.0.0\n   */\n  Warning = 'WARNING',\n\n  /**\n   * A notification feedback type indicating that a task has failed\n   *\n   * @since 1.0.0\n   */\n  Error = 'ERROR',\n}\n\nexport interface VibrateOptions {\n  /**\n   * Duration of the vibration in milliseconds.\n   *\n   * @default 300\n   * @since 1.0.0\n   */\n  duration: number;\n}\n"]}