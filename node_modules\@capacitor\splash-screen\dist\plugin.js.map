{"version": 3, "file": "plugin.js", "sources": ["esm/index.js", "esm/web.js"], "sourcesContent": ["import { registerPlugin } from '@capacitor/core';\nconst SplashScreen = registerPlugin('SplashScreen', {\n    web: () => import('./web').then(m => new m.SplashScreenWeb()),\n});\nexport * from './definitions';\nexport { SplashScreen };\n//# sourceMappingURL=index.js.map", "import { WebPlugin } from '@capacitor/core';\nexport class SplashScreenWeb extends WebPlugin {\n    async show(_options) {\n        return undefined;\n    }\n    async hide(_options) {\n        return undefined;\n    }\n}\n//# sourceMappingURL=web.js.map"], "names": ["registerPlugin", "WebPlugin"], "mappings": ";;;AACK,UAAC,YAAY,GAAGA,mBAAc,CAAC,cAAc,EAAE;IACpD,IAAI,GAAG,EAAE,MAAM,mDAAe,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,eAAe,EAAE,CAAC;IACjE,CAAC;;ICFM,MAAM,eAAe,SAASC,cAAS,CAAC;IAC/C,IAAI,MAAM,IAAI,CAAC,QAAQ,EAAE;IACzB,QAAQ,OAAO,SAAS;IACxB;IACA,IAAI,MAAM,IAAI,CAAC,QAAQ,EAAE;IACzB,QAAQ,OAAO,SAAS;IACxB;IACA;;;;;;;;;;;;;;;"}