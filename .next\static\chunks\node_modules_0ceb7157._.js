(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@capacitor/splash-screen/dist/esm/web.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@capacitor_splash-screen_dist_esm_web_5382f75b.js",
  "static/chunks/node_modules_@capacitor_splash-screen_dist_esm_web_ee8a2732.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@capacitor/splash-screen/dist/esm/web.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@capacitor/haptics/dist/esm/web.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@capacitor_haptics_dist_esm_web_e72b1f9e.js",
  "static/chunks/node_modules_@capacitor_haptics_dist_esm_web_ee8a2732.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@capacitor/haptics/dist/esm/web.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@capacitor/toast/dist/esm/web.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@capacitor_toast_dist_esm_web_36cf4c30.js",
  "static/chunks/node_modules_@capacitor_toast_dist_esm_web_ee8a2732.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@capacitor/toast/dist/esm/web.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@capacitor/share/dist/esm/web.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@capacitor_share_dist_esm_web_61d8b506.js",
  "static/chunks/node_modules_@capacitor_share_dist_esm_web_ee8a2732.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@capacitor/share/dist/esm/web.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@capacitor/network/dist/esm/web.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@capacitor_network_dist_esm_web_da610a88.js",
  "static/chunks/node_modules_@capacitor_network_dist_esm_web_ee8a2732.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@capacitor/network/dist/esm/web.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@capacitor/device/dist/esm/web.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_@capacitor_device_dist_esm_web_518e6e41.js",
  "static/chunks/node_modules_@capacitor_device_dist_esm_web_ee8a2732.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@capacitor/device/dist/esm/web.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@supabase/node-fetch/browser.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@supabase/node-fetch/browser.js [app-client] (ecmascript)");
    });
});
}}),
}]);