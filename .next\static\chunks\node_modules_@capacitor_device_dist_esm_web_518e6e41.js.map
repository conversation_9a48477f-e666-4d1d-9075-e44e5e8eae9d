{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "web.js", "sourceRoot": "", "sources": ["file:///C:/QuantAxiom/Products/LifeManager/node_modules/%40capacitor/device/src/web.ts"], "sourcesContent": ["import { WebPlugin } from '@capacitor/core';\n\nimport type {\n  BatteryInfo,\n  DeviceId,\n  DeviceInfo,\n  DevicePlugin,\n  GetLanguageCodeResult,\n  LanguageTag,\n} from './definitions';\n\ndeclare global {\n  interface Navigator {\n    getBattery: any;\n    oscpu: any;\n  }\n\n  interface Window {\n    InstallTrigger?: any;\n    ApplePaySession?: any;\n    chrome?: any;\n  }\n}\n\nexport class DeviceWeb extends WebPlugin implements DevicePlugin {\n  async getId(): Promise<DeviceId> {\n    return {\n      identifier: this.getUid(),\n    };\n  }\n\n  async getInfo(): Promise<DeviceInfo> {\n    if (typeof navigator === 'undefined' || !navigator.userAgent) {\n      throw this.unavailable('Device API not available in this browser');\n    }\n\n    const ua = navigator.userAgent;\n    const uaFields = this.parseUa(ua);\n\n    return {\n      model: uaFields.model,\n      platform: 'web' as const,\n      operatingSystem: uaFields.operatingSystem,\n      osVersion: uaFields.osVersion,\n      manufacturer: navigator.vendor,\n      isVirtual: false,\n      webViewVersion: uaFields.browserVersion,\n    };\n  }\n\n  async getBatteryInfo(): Promise<BatteryInfo> {\n    if (typeof navigator === 'undefined' || !navigator.getBattery) {\n      throw this.unavailable('Device API not available in this browser');\n    }\n    let battery: any = {};\n\n    try {\n      battery = await navigator.getBattery();\n    } catch (e) {\n      // Let it fail, we don't care\n    }\n\n    return {\n      batteryLevel: battery.level,\n      isCharging: battery.charging,\n    };\n  }\n\n  async getLanguageCode(): Promise<GetLanguageCodeResult> {\n    return {\n      value: navigator.language.split('-')[0].toLowerCase(),\n    };\n  }\n\n  async getLanguageTag(): Promise<LanguageTag> {\n    return {\n      value: navigator.language,\n    };\n  }\n\n  parseUa(ua: string): any {\n    const uaFields: any = {};\n    const start = ua.indexOf('(') + 1;\n    let end = ua.indexOf(') AppleWebKit');\n    if (ua.indexOf(') Gecko') !== -1) {\n      end = ua.indexOf(') Gecko');\n    }\n    const fields = ua.substring(start, end);\n    if (ua.indexOf('Android') !== -1) {\n      const tmpFields = fields.replace('; wv', '').split('; ').pop();\n      if (tmpFields) {\n        uaFields.model = tmpFields.split(' Build')[0];\n      }\n      uaFields.osVersion = fields.split('; ')[1];\n    } else {\n      uaFields.model = fields.split('; ')[0];\n      if (typeof navigator !== 'undefined' && navigator.oscpu) {\n        uaFields.osVersion = navigator.oscpu;\n      } else {\n        if (ua.indexOf('Windows') !== -1) {\n          uaFields.osVersion = fields;\n        } else {\n          const tmpFields = fields.split('; ').pop();\n          if (tmpFields) {\n            const lastParts = tmpFields\n              .replace(' like Mac OS X', '')\n              .split(' ');\n            uaFields.osVersion = lastParts[lastParts.length - 1].replace(\n              /_/g,\n              '.',\n            );\n          }\n        }\n      }\n    }\n\n    if (/android/i.test(ua)) {\n      uaFields.operatingSystem = 'android';\n    } else if (/iPad|iPhone|iPod/.test(ua) && !window.MSStream) {\n      uaFields.operatingSystem = 'ios';\n    } else if (/Win/.test(ua)) {\n      uaFields.operatingSystem = 'windows';\n    } else if (/Mac/i.test(ua)) {\n      uaFields.operatingSystem = 'mac';\n    } else {\n      uaFields.operatingSystem = 'unknown';\n    }\n\n    // Check for browsers based on non-standard javascript apis, only not user agent\n    const isSafari = !!window.ApplePaySession;\n    const isChrome = !!window.chrome;\n    const isFirefox = /Firefox/.test(ua);\n    const isEdge = /Edg/.test(ua);\n    const isFirefoxIOS = /FxiOS/.test(ua);\n    const isChromeIOS = /CriOS/.test(ua);\n    const isEdgeIOS = /EdgiOS/.test(ua);\n\n    // FF and Edge User Agents both end with \"/MAJOR.MINOR\"\n    if (\n      isSafari ||\n      (isChrome && !isEdge) ||\n      isFirefoxIOS ||\n      isChromeIOS ||\n      isEdgeIOS\n    ) {\n      // Safari version comes as     \"... Version/MAJOR.MINOR ...\"\n      // Chrome version comes as     \"... Chrome/MAJOR.MINOR ...\"\n      // FirefoxIOS version comes as \"... FxiOS/MAJOR.MINOR ...\"\n      // ChromeIOS version comes as  \"... CriOS/MAJOR.MINOR ...\"\n      let searchWord: string;\n      if (isFirefoxIOS) {\n        searchWord = 'FxiOS';\n      } else if (isChromeIOS) {\n        searchWord = 'CriOS';\n      } else if (isEdgeIOS) {\n        searchWord = 'EdgiOS';\n      } else if (isSafari) {\n        searchWord = 'Version';\n      } else {\n        searchWord = 'Chrome';\n      }\n\n      const words = ua.split(' ');\n      for (const word of words) {\n        if (word.includes(searchWord)) {\n          const version = word.split('/')[1];\n          uaFields.browserVersion = version;\n        }\n      }\n    } else if (isFirefox || isEdge) {\n      const reverseUA = ua.split('').reverse().join('');\n      const reverseVersion = reverseUA.split('/')[0];\n      const version = reverseVersion.split('').reverse().join('');\n      uaFields.browserVersion = version;\n    }\n\n    return uaFields;\n  }\n\n  getUid(): string {\n    if (typeof window !== 'undefined' && window.localStorage) {\n      let uid = window.localStorage.getItem('_capuid');\n      if (uid) {\n        return uid;\n      }\n\n      uid = this.uuid4();\n      window.localStorage.setItem('_capuid', uid);\n      return uid;\n    }\n    return this.uuid4();\n  }\n\n  uuid4(): string {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(\n      /[xy]/g,\n      function (c) {\n        const r = (Math.random() * 16) | 0,\n          v = c === 'x' ? r : (r & 0x3) | 0x8;\n        return v.toString(16);\n      },\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;;AAwBtC,MAAO,SAAU,iKAAQ,YAAS;IACtC,KAAK,CAAC,KAAK,GAAA;QACT,OAAO;YACL,UAAU,EAAE,IAAI,CAAC,MAAM,EAAE;SAC1B,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,OAAO,GAAA;QACX,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;YAC5D,MAAM,IAAI,CAAC,WAAW,CAAC,0CAA0C,CAAC,CAAC;SACpE;QAED,MAAM,EAAE,GAAG,SAAS,CAAC,SAAS,CAAC;QAC/B,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAElC,OAAO;YACL,KAAK,EAAE,QAAQ,CAAC,KAAK;YACrB,QAAQ,EAAE,KAAc;YACxB,eAAe,EAAE,QAAQ,CAAC,eAAe;YACzC,SAAS,EAAE,QAAQ,CAAC,SAAS;YAC7B,YAAY,EAAE,SAAS,CAAC,MAAM;YAC9B,SAAS,EAAE,KAAK;YAChB,cAAc,EAAE,QAAQ,CAAC,cAAc;SACxC,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,cAAc,GAAA;QAClB,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE;YAC7D,MAAM,IAAI,CAAC,WAAW,CAAC,0CAA0C,CAAC,CAAC;SACpE;QACD,IAAI,OAAO,GAAQ,CAAA,CAAE,CAAC;QAEtB,IAAI;YACF,OAAO,GAAG,MAAM,SAAS,CAAC,UAAU,EAAE,CAAC;SACxC,CAAC,OAAO,CAAC,EAAE;QACV,6BAA6B;SAC9B;QAED,OAAO;YACL,YAAY,EAAE,OAAO,CAAC,KAAK;YAC3B,UAAU,EAAE,OAAO,CAAC,QAAQ;SAC7B,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,eAAe,GAAA;QACnB,OAAO;YACL,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE;SACtD,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,cAAc,GAAA;QAClB,OAAO;YACL,KAAK,EAAE,SAAS,CAAC,QAAQ;SAC1B,CAAC;IACJ,CAAC;IAED,OAAO,CAAC,EAAU,EAAA;QAChB,MAAM,QAAQ,GAAQ,CAAA,CAAE,CAAC;QACzB,MAAM,KAAK,GAAG,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAClC,IAAI,GAAG,GAAG,EAAE,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;QACtC,IAAI,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE;YAChC,GAAG,GAAG,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;SAC7B;QACD,MAAM,MAAM,GAAG,EAAE,CAAC,SAAS,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QACxC,IAAI,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE;YAChC,MAAM,SAAS,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC;YAC/D,IAAI,SAAS,EAAE;gBACb,QAAQ,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;aAC/C;YACD,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;SAC5C,MAAM;YACL,QAAQ,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YACvC,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,KAAK,EAAE;gBACvD,QAAQ,CAAC,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC;aACtC,MAAM;gBACL,IAAI,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE;oBAChC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC;iBAC7B,MAAM;oBACL,MAAM,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC;oBAC3C,IAAI,SAAS,EAAE;wBACb,MAAM,SAAS,GAAG,SAAS,CACxB,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAC7B,KAAK,CAAC,GAAG,CAAC,CAAC;wBACd,QAAQ,CAAC,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,OAAO,CAC1D,IAAI,EACJ,GAAG,CACJ,CAAC;qBACH;iBACF;aACF;SACF;QAED,IAAI,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;YACvB,QAAQ,CAAC,eAAe,GAAG,SAAS,CAAC;SACtC,MAAM,IAAI,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;YAC1D,QAAQ,CAAC,eAAe,GAAG,KAAK,CAAC;SAClC,MAAM,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;YACzB,QAAQ,CAAC,eAAe,GAAG,SAAS,CAAC;SACtC,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;YAC1B,QAAQ,CAAC,eAAe,GAAG,KAAK,CAAC;SAClC,MAAM;YACL,QAAQ,CAAC,eAAe,GAAG,SAAS,CAAC;SACtC;QAED,gFAAgF;QAChF,MAAM,QAAQ,GAAG,CAAC,CAAC,MAAM,CAAC,eAAe,CAAC;QAC1C,MAAM,QAAQ,GAAG,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC;QACjC,MAAM,SAAS,GAAG,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACrC,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC9B,MAAM,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACtC,MAAM,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACrC,MAAM,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAEpC,uDAAuD;QACvD,IACE,QAAQ,IACP,QAAQ,IAAI,CAAC,MAAM,CAAC,GACrB,YAAY,IACZ,WAAW,IACX,SAAS,EACT;YACA,4DAA4D;YAC5D,2DAA2D;YAC3D,0DAA0D;YAC1D,0DAA0D;YAC1D,IAAI,UAAkB,CAAC;YACvB,IAAI,YAAY,EAAE;gBAChB,UAAU,GAAG,OAAO,CAAC;aACtB,MAAM,IAAI,WAAW,EAAE;gBACtB,UAAU,GAAG,OAAO,CAAC;aACtB,MAAM,IAAI,SAAS,EAAE;gBACpB,UAAU,GAAG,QAAQ,CAAC;aACvB,MAAM,IAAI,QAAQ,EAAE;gBACnB,UAAU,GAAG,SAAS,CAAC;aACxB,MAAM;gBACL,UAAU,GAAG,QAAQ,CAAC;aACvB;YAED,MAAM,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC5B,KAAK,MAAM,IAAI,IAAI,KAAK,CAAE;gBACxB,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE;oBAC7B,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;oBACnC,QAAQ,CAAC,cAAc,GAAG,OAAO,CAAC;iBACnC;aACF;SACF,MAAM,IAAI,SAAS,IAAI,MAAM,EAAE;YAC9B,MAAM,SAAS,GAAG,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAClD,MAAM,cAAc,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/C,MAAM,OAAO,GAAG,cAAc,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC5D,QAAQ,CAAC,cAAc,GAAG,OAAO,CAAC;SACnC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,MAAM,GAAA;QACJ,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,YAAY,EAAE;YACxD,IAAI,GAAG,GAAG,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YACjD,IAAI,GAAG,EAAE;gBACP,OAAO,GAAG,CAAC;aACZ;YAED,GAAG,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;YACnB,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;YAC5C,OAAO,GAAG,CAAC;SACZ;QACD,OAAO,IAAI,CAAC,KAAK,EAAE,CAAC;IACtB,CAAC;IAED,KAAK,GAAA;QACH,OAAO,sCAAsC,CAAC,OAAO,CACnD,OAAO,EACP,SAAU,CAAC;YACT,MAAM,CAAC,GAAG,AAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,EAAG,CAAC,EAChC,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,AAAC,CAAC,GAAG,GAAG,CAAC,EAAG,GAAG,CAAC;YACtC,OAAO,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QACxB,CAAC,CACF,CAAC;IACJ,CAAC;CACF", "ignoreList": [0], "debugId": null}}]}