{"version": 3, "file": "definitions.js", "sourceRoot": "", "sources": ["../../src/definitions.ts"], "names": [], "mappings": "AAAA,wCAAwC", "sourcesContent": ["/// <reference types=\"@capacitor/cli\" />\n\ndeclare module '@capacitor/cli' {\n  export interface PluginsConfig {\n    /**\n     * These config values are available:\n     */\n    SplashScreen?: {\n      /**\n       * How long to show the launch splash screen when autoHide is enabled (in ms)\n       *\n       * @since 1.0.0\n       * @default 500\n       * @example 3000\n       */\n      launchShowDuration?: number;\n\n      /**\n       * Whether to auto hide the splash after launchShowDuration.\n       *\n       * @since 1.0.0\n       * @default true\n       * @example true\n       */\n      launchAutoHide?: boolean;\n\n      /**\n       * Duration for the fade out animation of the launch splash screen (in ms)\n       *\n       * Only available for Android, when using the Android 12 Splash Screen API.\n       *\n       * @since 4.2.0\n       * @default 200\n       * @example 3000\n       */\n      launchFadeOutDuration?: number;\n\n      /**\n       * Color of the background of the Splash Screen in hex format, #RRGGBB or #RRGGBBAA.\n       * Doesn't work if `useDialog` is true or on launch when using the Android 12 API.\n       *\n       * @since 1.0.0\n       * @example \"#ffffffff\"\n       */\n      backgroundColor?: string;\n\n      /**\n       * Name of the resource to be used as Splash Screen.\n       *\n       * Doesn't work on launch when using the Android 12 API.\n       *\n       * Only available on Android.\n       *\n       * @since 1.0.0\n       * @default splash\n       * @example \"splash\"\n       */\n      androidSplashResourceName?: string;\n\n      /**\n       * The [ImageView.ScaleType](https://developer.android.com/reference/android/widget/ImageView.ScaleType) used to scale\n       * the Splash Screen image.\n       * Doesn't work if `useDialog` is true or on launch when using the Android 12 API.\n       *\n       * Only available on Android.\n       *\n       * @since 1.0.0\n       * @default FIT_XY\n       * @example \"CENTER_CROP\"\n       */\n      androidScaleType?:\n        | 'CENTER'\n        | 'CENTER_CROP'\n        | 'CENTER_INSIDE'\n        | 'FIT_CENTER'\n        | 'FIT_END'\n        | 'FIT_START'\n        | 'FIT_XY'\n        | 'MATRIX';\n\n      /**\n       * Show a loading spinner on the Splash Screen.\n       * Doesn't work if `useDialog` is true or on launch when using the Android 12 API.\n       *\n       * @since 1.0.0\n       * @example true\n       */\n      showSpinner?: boolean;\n\n      /**\n       * Style of the Android spinner.\n       * Doesn't work if `useDialog` is true or on launch when using the Android 12 API.\n       *\n       * @since 1.0.0\n       * @default large\n       * @example \"large\"\n       */\n      androidSpinnerStyle?:\n        | 'horizontal'\n        | 'small'\n        | 'large'\n        | 'inverse'\n        | 'smallInverse'\n        | 'largeInverse';\n\n      /**\n       * Style of the iOS spinner.\n       * Doesn't work if `useDialog` is true.\n       *\n       * Only available on iOS.\n       *\n       * @since 1.0.0\n       * @default large\n       * @example \"small\"\n       */\n      iosSpinnerStyle?: 'large' | 'small';\n\n      /**\n       * Color of the spinner in hex format, #RRGGBB or #RRGGBBAA.\n       * Doesn't work if `useDialog` is true or on launch when using the Android 12 API.\n       *\n       * @since 1.0.0\n       * @example \"#999999\"\n       */\n      spinnerColor?: string;\n\n      /**\n       * Hide the status bar on the Splash Screen.\n       *\n       * Doesn't work on launch when using the Android 12 API.\n       *\n       * Only available on Android.\n       *\n       * @since 1.0.0\n       * @example true\n       */\n      splashFullScreen?: boolean;\n\n      /**\n       * Hide the status bar and the software navigation buttons on the Splash Screen.\n       *\n       * Doesn't work on launch when using the Android 12 API.\n       *\n       * Only available on Android.\n       *\n       * @since 1.0.0\n       * @example true\n       */\n      splashImmersive?: boolean;\n\n      /**\n       * If `useDialog` is set to true, configure the Dialog layout.\n       * If `useDialog` is not set or false, use a layout instead of the ImageView.\n       *\n       * Doesn't work on launch when using the Android 12 API.\n       *\n       * Only available on Android.\n       *\n       * @since 1.1.0\n       * @example \"launch_screen\"\n       */\n      layoutName?: string;\n\n      /**\n       * Use a Dialog instead of an ImageView.\n       * If `layoutName` is not configured, it will use\n       * a layout that uses the splash image as background.\n       *\n       * Doesn't work on launch when using the Android 12 API.\n       *\n       * Only available on Android.\n       *\n       * @since 1.1.0\n       * @example true\n       */\n      useDialog?: boolean;\n    };\n  }\n}\n\nexport interface ShowOptions {\n  /**\n   * Whether to auto hide the splash after showDuration\n   *\n   * @since 1.0.0\n   */\n  autoHide?: boolean;\n  /**\n   * How long (in ms) to fade in.\n   *\n   * @since 1.0.0\n   * @default 200\n   */\n  fadeInDuration?: number;\n  /**\n   * How long (in ms) to fade out.\n   *\n   * @since 1.0.0\n   * @default 200\n   */\n  fadeOutDuration?: number;\n  /**\n   * How long to show the splash screen when autoHide is enabled (in ms)\n   *\n   * @since 1.0.0\n   * @default 3000\n   */\n  showDuration?: number;\n}\n\nexport interface HideOptions {\n  /**\n   * How long (in ms) to fade out.\n   *\n   * On Android, if using the Android 12 Splash Screen API, it's not being used.\n   * Use launchFadeOutDuration configuration option instead.\n   *\n   * @since 1.0.0\n   * @default 200\n   */\n  fadeOutDuration?: number;\n}\n\nexport interface SplashScreenPlugin {\n  /**\n   * Show the splash screen\n   *\n   * @since 1.0.0\n   */\n  show(options?: ShowOptions): Promise<void>;\n  /**\n   * Hide the splash screen\n   *\n   * @since 1.0.0\n   */\n  hide(options?: HideOptions): Promise<void>;\n}\n"]}