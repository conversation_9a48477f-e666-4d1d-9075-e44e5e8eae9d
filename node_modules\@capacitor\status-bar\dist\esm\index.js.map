{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,cAAc,EAAE,MAAM,iBAAiB,CAAC;AAIjD,MAAM,SAAS,GAAG,cAAc,CAAkB,WAAW,CAAC,CAAC;AAE/D,cAAc,eAAe,CAAC;AAC9B,OAAO,EAAE,SAAS,EAAE,CAAC", "sourcesContent": ["import { registerPlugin } from '@capacitor/core';\n\nimport type { StatusBarPlugin } from './definitions';\n\nconst StatusBar = registerPlugin<StatusBarPlugin>('StatusBar');\n\nexport * from './definitions';\nexport { StatusBar };\n"]}