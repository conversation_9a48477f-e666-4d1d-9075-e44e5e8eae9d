{"version": 3, "file": "definitions.js", "sourceRoot": "", "sources": ["../../src/definitions.ts"], "names": [], "mappings": "", "sourcesContent": ["export interface ToastPlugin {\n  /**\n   * Shows a Toast on the screen\n   *\n   * @since 1.0.0\n   */\n  show(options: ShowOptions): Promise<void>;\n}\n\nexport interface ShowOptions {\n  /**\n   * Text to display on the Toast\n   *\n   * @since 1.0.0\n   */\n  text: string;\n\n  /**\n   * Duration of the Toast, either 'short' (2000ms) or 'long' (3500ms)\n   *\n   * @default 'short'\n   * @since 1.0.0\n   */\n  duration?: 'short' | 'long';\n\n  /**\n   * Position of the Toast.\n   *\n   * On Android 12 and newer all toasts are shown at the bottom.\n   *\n   * @default 'bottom'\n   * @since 1.0.0\n   */\n  position?: 'top' | 'center' | 'bottom';\n}\n\n/**\n * @deprecated Use `ToastShowOptions`.\n * @since 1.0.0\n */\nexport type ToastShowOptions = ShowOptions;\n"]}