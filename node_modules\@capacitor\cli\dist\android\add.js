"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createLocalProperties = exports.addAndroid = void 0;
const tslib_1 = require("tslib");
const fs_extra_1 = require("fs-extra");
const os_1 = require("os");
const path_1 = require("path");
const colors_1 = tslib_1.__importDefault(require("../colors"));
const common_1 = require("../common");
const subprocess_1 = require("../util/subprocess");
const template_1 = require("../util/template");
async function addAndroid(config) {
    await (0, common_1.runTask)(`Adding native android project in ${colors_1.default.strong(config.android.platformDir)}`, async () => {
        return (0, template_1.extractTemplate)(config.cli.assets.android.platformTemplateArchiveAbs, config.android.platformDirAbs);
    });
}
exports.addAndroid = addAndroid;
async function createLocalProperties(platformDir) {
    const defaultAndroidPath = (0, path_1.join)((0, os_1.homedir)(), 'Library/Android/sdk');
    if (await (0, fs_extra_1.pathExists)(defaultAndroidPath)) {
        const localSettings = `
## This file is automatically generated by Android Studio.
# Do not modify this file -- YOUR CHANGES WILL BE ERASED!
#
# This file should *NOT* be checked into Version Control Systems,
# as it contains information specific to your local configuration.
#
# Location of the SDK. This is only used by Gradle.
# For customization when using a Version Control System, please read the
# header note.
sdk.dir=${defaultAndroidPath}
  `;
        await (0, fs_extra_1.writeFile)((0, path_1.join)(platformDir, 'local.properties'), localSettings, {
            encoding: 'utf-8',
        });
        // Only sync if we were able to create the local properties above, otherwise
        // this will fail
        try {
            await gradleSync(platformDir);
        }
        catch (e) {
            console.error('Error running gradle sync', e);
            console.error('Unable to infer default Android SDK settings. This is fine, just run npx cap open android and import and sync gradle manually');
        }
    }
}
exports.createLocalProperties = createLocalProperties;
async function gradleSync(platformDir) {
    await (0, subprocess_1.runCommand)(`./gradlew`, [], {
        cwd: platformDir,
    });
}
