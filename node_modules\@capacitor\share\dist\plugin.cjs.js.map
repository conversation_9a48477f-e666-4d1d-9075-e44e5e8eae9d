{"version": 3, "file": "plugin.cjs.js", "sources": ["esm/index.js", "esm/web.js"], "sourcesContent": ["import { registerPlugin } from '@capacitor/core';\nconst Share = registerPlugin('Share', {\n    web: () => import('./web').then(m => new m.ShareWeb()),\n});\nexport * from './definitions';\nexport { Share };\n//# sourceMappingURL=index.js.map", "import { WebPlugin } from '@capacitor/core';\nexport class ShareWeb extends WebPlugin {\n    async canShare() {\n        if (typeof navigator === 'undefined' || !navigator.share) {\n            return { value: false };\n        }\n        else {\n            return { value: true };\n        }\n    }\n    async share(options) {\n        if (typeof navigator === 'undefined' || !navigator.share) {\n            throw this.unavailable('Share API not available in this browser');\n        }\n        await navigator.share({\n            title: options.title,\n            text: options.text,\n            url: options.url,\n        });\n        return {};\n    }\n}\n//# sourceMappingURL=web.js.map"], "names": ["registerPlugin", "WebPlugin"], "mappings": ";;;;AACK,MAAC,KAAK,GAAGA,mBAAc,CAAC,OAAO,EAAE;AACtC,IAAI,GAAG,EAAE,MAAM,mDAAe,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC;AAC1D,CAAC;;ACFM,MAAM,QAAQ,SAASC,cAAS,CAAC;AACxC,IAAI,MAAM,QAAQ,GAAG;AACrB,QAAQ,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;AAClE,YAAY,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE;AACnC;AACA,aAAa;AACb,YAAY,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE;AAClC;AACA;AACA,IAAI,MAAM,KAAK,CAAC,OAAO,EAAE;AACzB,QAAQ,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;AAClE,YAAY,MAAM,IAAI,CAAC,WAAW,CAAC,yCAAyC,CAAC;AAC7E;AACA,QAAQ,MAAM,SAAS,CAAC,KAAK,CAAC;AAC9B,YAAY,KAAK,EAAE,OAAO,CAAC,KAAK;AAChC,YAAY,IAAI,EAAE,OAAO,CAAC,IAAI;AAC9B,YAAY,GAAG,EAAE,OAAO,CAAC,GAAG;AAC5B,SAAS,CAAC;AACV,QAAQ,OAAO,EAAE;AACjB;AACA;;;;;;;;;"}