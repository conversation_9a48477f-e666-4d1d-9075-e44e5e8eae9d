{"version": 3, "file": "plugin.js", "sources": ["esm/index.js", "esm/web.js"], "sourcesContent": ["import { registerPlugin } from '@capacitor/core';\nconst Network = registerPlugin('Network', {\n    web: () => import('./web').then(m => new m.NetworkWeb()),\n});\nexport * from './definitions';\nexport { Network };\n//# sourceMappingURL=index.js.map", "import { WebPlugin } from '@capacitor/core';\nfunction translatedConnection() {\n    const connection = window.navigator.connection ||\n        window.navigator.mozConnection ||\n        window.navigator.webkitConnection;\n    let result = 'unknown';\n    const type = connection ? connection.type || connection.effectiveType : null;\n    if (type && typeof type === 'string') {\n        switch (type) {\n            // possible type values\n            case 'bluetooth':\n            case 'cellular':\n                result = 'cellular';\n                break;\n            case 'none':\n                result = 'none';\n                break;\n            case 'ethernet':\n            case 'wifi':\n            case 'wimax':\n                result = 'wifi';\n                break;\n            case 'other':\n            case 'unknown':\n                result = 'unknown';\n                break;\n            // possible effectiveType values\n            case 'slow-2g':\n            case '2g':\n            case '3g':\n                result = 'cellular';\n                break;\n            case '4g':\n                result = 'wifi';\n                break;\n            default:\n                break;\n        }\n    }\n    return result;\n}\nexport class NetworkWeb extends WebPlugin {\n    constructor() {\n        super();\n        this.handleOnline = () => {\n            const connectionType = translatedConnection();\n            const status = {\n                connected: true,\n                connectionType: connectionType,\n            };\n            this.notifyListeners('networkStatusChange', status);\n        };\n        this.handleOffline = () => {\n            const status = {\n                connected: false,\n                connectionType: 'none',\n            };\n            this.notifyListeners('networkStatusChange', status);\n        };\n        if (typeof window !== 'undefined') {\n            window.addEventListener('online', this.handleOnline);\n            window.addEventListener('offline', this.handleOffline);\n        }\n    }\n    async getStatus() {\n        if (!window.navigator) {\n            throw this.unavailable('Browser does not support the Network Information API');\n        }\n        const connected = window.navigator.onLine;\n        const connectionType = translatedConnection();\n        const status = {\n            connected,\n            connectionType: connected ? connectionType : 'none',\n        };\n        return status;\n    }\n}\nconst Network = new NetworkWeb();\nexport { Network };\n//# sourceMappingURL=web.js.map"], "names": ["Network", "registerPlugin", "WebPlugin"], "mappings": ";;;AACK,UAACA,SAAO,GAAGC,mBAAc,CAAC,SAAS,EAAE;IAC1C,IAAI,GAAG,EAAE,MAAM,mDAAe,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,UAAU,EAAE,CAAC;IAC5D,CAAC;;ICFD,SAAS,oBAAoB,GAAG;IAChC,IAAI,MAAM,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC,UAAU;IAClD,QAAQ,MAAM,CAAC,SAAS,CAAC,aAAa;IACtC,QAAQ,MAAM,CAAC,SAAS,CAAC,gBAAgB;IACzC,IAAI,IAAI,MAAM,GAAG,SAAS;IAC1B,IAAI,MAAM,IAAI,GAAG,UAAU,GAAG,UAAU,CAAC,IAAI,IAAI,UAAU,CAAC,aAAa,GAAG,IAAI;IAChF,IAAI,IAAI,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;IAC1C,QAAQ,QAAQ,IAAI;IACpB;IACA,YAAY,KAAK,WAAW;IAC5B,YAAY,KAAK,UAAU;IAC3B,gBAAgB,MAAM,GAAG,UAAU;IACnC,gBAAgB;IAChB,YAAY,KAAK,MAAM;IACvB,gBAAgB,MAAM,GAAG,MAAM;IAC/B,gBAAgB;IAChB,YAAY,KAAK,UAAU;IAC3B,YAAY,KAAK,MAAM;IACvB,YAAY,KAAK,OAAO;IACxB,gBAAgB,MAAM,GAAG,MAAM;IAC/B,gBAAgB;IAChB,YAAY,KAAK,OAAO;IACxB,YAAY,KAAK,SAAS;IAC1B,gBAAgB,MAAM,GAAG,SAAS;IAClC,gBAAgB;IAChB;IACA,YAAY,KAAK,SAAS;IAC1B,YAAY,KAAK,IAAI;IACrB,YAAY,KAAK,IAAI;IACrB,gBAAgB,MAAM,GAAG,UAAU;IACnC,gBAAgB;IAChB,YAAY,KAAK,IAAI;IACrB,gBAAgB,MAAM,GAAG,MAAM;IAC/B,gBAAgB;IAGhB;IACA;IACA,IAAI,OAAO,MAAM;IACjB;IACO,MAAM,UAAU,SAASC,cAAS,CAAC;IAC1C,IAAI,WAAW,GAAG;IAClB,QAAQ,KAAK,EAAE;IACf,QAAQ,IAAI,CAAC,YAAY,GAAG,MAAM;IAClC,YAAY,MAAM,cAAc,GAAG,oBAAoB,EAAE;IACzD,YAAY,MAAM,MAAM,GAAG;IAC3B,gBAAgB,SAAS,EAAE,IAAI;IAC/B,gBAAgB,cAAc,EAAE,cAAc;IAC9C,aAAa;IACb,YAAY,IAAI,CAAC,eAAe,CAAC,qBAAqB,EAAE,MAAM,CAAC;IAC/D,SAAS;IACT,QAAQ,IAAI,CAAC,aAAa,GAAG,MAAM;IACnC,YAAY,MAAM,MAAM,GAAG;IAC3B,gBAAgB,SAAS,EAAE,KAAK;IAChC,gBAAgB,cAAc,EAAE,MAAM;IACtC,aAAa;IACb,YAAY,IAAI,CAAC,eAAe,CAAC,qBAAqB,EAAE,MAAM,CAAC;IAC/D,SAAS;IACT,QAAQ,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;IAC3C,YAAY,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC;IAChE,YAAY,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC;IAClE;IACA;IACA,IAAI,MAAM,SAAS,GAAG;IACtB,QAAQ,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;IAC/B,YAAY,MAAM,IAAI,CAAC,WAAW,CAAC,sDAAsD,CAAC;IAC1F;IACA,QAAQ,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,MAAM;IACjD,QAAQ,MAAM,cAAc,GAAG,oBAAoB,EAAE;IACrD,QAAQ,MAAM,MAAM,GAAG;IACvB,YAAY,SAAS;IACrB,YAAY,cAAc,EAAE,SAAS,GAAG,cAAc,GAAG,MAAM;IAC/D,SAAS;IACT,QAAQ,OAAO,MAAM;IACrB;IACA;IACA,MAAM,OAAO,GAAG,IAAI,UAAU,EAAE;;;;;;;;;;;;;;;;"}