{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "web.js", "sourceRoot": "", "sources": ["file:///C:/QuantAxiom/Products/LifeManager/node_modules/%40capacitor/splash-screen/src/web.ts"], "sourcesContent": ["import { WebPlugin } from '@capacitor/core';\n\nimport type {\n  HideOptions,\n  ShowOptions,\n  SplashScreenPlugin,\n} from './definitions';\n\nexport class SplashScreenWeb extends WebPlugin implements SplashScreenPlugin {\n  async show(_options?: ShowOptions): Promise<void> {\n    return undefined;\n  }\n\n  async hide(_options?: HideOptions): Promise<void> {\n    return undefined;\n  }\n}\n"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;;AAQtC,MAAO,eAAgB,8JAAQ,YAAS;IAC5C,KAAK,CAAC,IAAI,CAAC,QAAsB,EAAA;QAC/B,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,QAAsB,EAAA;QAC/B,OAAO,SAAS,CAAC;IACnB,CAAC;CACF", "ignoreList": [0], "debugId": null}}]}