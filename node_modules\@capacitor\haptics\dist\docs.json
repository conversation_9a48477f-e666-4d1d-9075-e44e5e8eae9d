{"api": {"name": "HapticsPlugin", "slug": "hapticsplugin", "docs": "", "tags": [], "methods": [{"name": "impact", "signature": "(options?: ImpactOptions | undefined) => Promise<void>", "parameters": [{"name": "options", "docs": "", "type": "ImpactOptions | undefined"}], "returns": "Promise<void>", "tags": [{"name": "since", "text": "1.0.0"}], "docs": "Trigger a haptics \"impact\" feedback", "complexTypes": ["ImpactOptions"], "slug": "impact"}, {"name": "notification", "signature": "(options?: NotificationOptions | undefined) => Promise<void>", "parameters": [{"name": "options", "docs": "", "type": "NotificationOptions | undefined"}], "returns": "Promise<void>", "tags": [{"name": "since", "text": "1.0.0"}], "docs": "Trigger a haptics \"notification\" feedback", "complexTypes": ["NotificationOptions"], "slug": "notification"}, {"name": "vibrate", "signature": "(options?: VibrateOptions | undefined) => Promise<void>", "parameters": [{"name": "options", "docs": "", "type": "VibrateOptions | undefined"}], "returns": "Promise<void>", "tags": [{"name": "since", "text": "1.0.0"}], "docs": "Vibrate the device", "complexTypes": ["VibrateOptions"], "slug": "vibrate"}, {"name": "selectionStart", "signature": "() => Promise<void>", "parameters": [], "returns": "Promise<void>", "tags": [{"name": "since", "text": "1.0.0"}], "docs": "Trigger a selection started haptic hint", "complexTypes": [], "slug": "selectionstart"}, {"name": "selectionChanged", "signature": "() => Promise<void>", "parameters": [], "returns": "Promise<void>", "tags": [{"name": "since", "text": "1.0.0"}], "docs": "Trigger a selection changed haptic hint. If a selection was\nstarted already, this will cause the device to provide haptic\nfeedback", "complexTypes": [], "slug": "selectionchanged"}, {"name": "selectionEnd", "signature": "() => Promise<void>", "parameters": [], "returns": "Promise<void>", "tags": [{"name": "since", "text": "1.0.0"}], "docs": "If selectionStart() was called, selectionEnd() ends the selection.\nFor example, call this when a user has lifted their finger from a control", "complexTypes": [], "slug": "selectionend"}], "properties": []}, "interfaces": [{"name": "ImpactOptions", "slug": "impactoptions", "docs": "", "tags": [], "methods": [], "properties": [{"name": "style", "tags": [{"text": "ImpactStyle.Heavy", "name": "default"}, {"text": "1.0.0", "name": "since"}], "docs": "Impact Feedback Style\n\nThe mass of the objects in the collision simulated by a [UIImpactFeedbackGenerator](https://developer.apple.com/documentation/uikit/uiimpactfeedbackstyle) object.", "complexTypes": ["ImpactStyle"], "type": "ImpactStyle"}]}, {"name": "NotificationOptions", "slug": "notificationoptions", "docs": "", "tags": [], "methods": [], "properties": [{"name": "type", "tags": [{"text": "NotificationType.SUCCESS", "name": "default"}, {"text": "1.0.0", "name": "since"}], "docs": "Notification Feedback Type\n\nThe type of notification feedback generated by a [UINotificationFeedbackGenerator](https://developer.apple.com/documentation/uikit/uinotificationfeedbacktype) object.", "complexTypes": ["NotificationType"], "type": "NotificationType"}]}, {"name": "VibrateOptions", "slug": "vibrateoptions", "docs": "", "tags": [], "methods": [], "properties": [{"name": "duration", "tags": [{"text": "300", "name": "default"}, {"text": "1.0.0", "name": "since"}], "docs": "Duration of the vibration in milliseconds.", "complexTypes": [], "type": "number"}]}], "enums": [{"name": "ImpactStyle", "slug": "impactstyle", "members": [{"name": "Heavy", "value": "'HEAVY'", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "A collision between large, heavy user interface elements"}, {"name": "Medium", "value": "'MEDIUM'", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "A collision between moderately sized user interface elements"}, {"name": "Light", "value": "'LIGHT'", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "A collision between small, light user interface elements"}]}, {"name": "NotificationType", "slug": "notificationtype", "members": [{"name": "Success", "value": "'SUCCESS'", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "A notification feedback type indicating that a task has completed successfully"}, {"name": "Warning", "value": "'WARNING'", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "A notification feedback type indicating that a task has produced a warning"}, {"name": "Error", "value": "'ERROR'", "tags": [{"text": "1.0.0", "name": "since"}], "docs": "A notification feedback type indicating that a task has failed"}]}], "typeAliases": [], "pluginConfigs": []}