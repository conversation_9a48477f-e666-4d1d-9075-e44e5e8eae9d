{"version": 3, "file": "plugin.cjs.js", "sources": ["esm/definitions.js", "esm/index.js"], "sourcesContent": ["/// <reference types=\"@capacitor/cli\" />\nexport var Style;\n(function (Style) {\n    /**\n     * Light text for dark backgrounds.\n     *\n     * @since 1.0.0\n     */\n    Style[\"Dark\"] = \"DARK\";\n    /**\n     * Dark text for light backgrounds.\n     *\n     * @since 1.0.0\n     */\n    Style[\"Light\"] = \"LIGHT\";\n    /**\n     * The style is based on the device appearance.\n     * If the device is using Dark mode, the statusbar text will be light.\n     * If the device is using Light mode, the statusbar text will be dark.\n     *\n     * @since 1.0.0\n     */\n    Style[\"Default\"] = \"DEFAULT\";\n})(Style || (Style = {}));\nexport var Animation;\n(function (Animation) {\n    /**\n     * No animation during show/hide.\n     *\n     * @since 1.0.0\n     */\n    Animation[\"None\"] = \"NONE\";\n    /**\n     * Slide animation during show/hide.\n     * It doesn't work on iOS 15+.\n     *\n     * @deprecated Use Animation.Fade or Animation.None instead.\n     *\n     * @since 1.0.0\n     */\n    Animation[\"Slide\"] = \"SLIDE\";\n    /**\n     * Fade animation during show/hide.\n     *\n     * @since 1.0.0\n     */\n    Animation[\"Fade\"] = \"FADE\";\n})(Animation || (Animation = {}));\n/**\n * @deprecated Use `Animation`.\n * @since 1.0.0\n */\nexport const StatusBarAnimation = Animation;\n/**\n * @deprecated Use `Style`.\n * @since 1.0.0\n */\nexport const StatusBarStyle = Style;\n//# sourceMappingURL=definitions.js.map", "import { registerPlugin } from '@capacitor/core';\nconst StatusBar = registerPlugin('StatusBar');\nexport * from './definitions';\nexport { StatusBar };\n//# sourceMappingURL=index.js.map"], "names": ["Style", "Animation", "registerPlugin"], "mappings": ";;;;AAAA;AACWA;AACX,CAAC,UAAU,KAAK,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA,IAAI,KAAK,CAAC,MAAM,CAAC,GAAG,MAAM;AAC1B;AACA;AACA;AACA;AACA;AACA,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,OAAO;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,KAAK,CAAC,SAAS,CAAC,GAAG,SAAS;AAChC,CAAC,EAAEA,aAAK,KAAKA,aAAK,GAAG,EAAE,CAAC,CAAC;AACdC;AACX,CAAC,UAAU,SAAS,EAAE;AACtB;AACA;AACA;AACA;AACA;AACA,IAAI,SAAS,CAAC,MAAM,CAAC,GAAG,MAAM;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,SAAS,CAAC,OAAO,CAAC,GAAG,OAAO;AAChC;AACA;AACA;AACA;AACA;AACA,IAAI,SAAS,CAAC,MAAM,CAAC,GAAG,MAAM;AAC9B,CAAC,EAAEA,iBAAS,KAAKA,iBAAS,GAAG,EAAE,CAAC,CAAC;AACjC;AACA;AACA;AACA;AACY,MAAC,kBAAkB,GAAGA;AAClC;AACA;AACA;AACA;AACY,MAAC,cAAc,GAAGD;;ACxDzB,MAAC,SAAS,GAAGE,mBAAc,CAAC,WAAW;;;;;;"}